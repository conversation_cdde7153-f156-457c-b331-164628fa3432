/**
 * 趋势图绘制模块
 * 使用Canvas 2D API绘制练习趋势图
 */

class TrendChart {
  
  constructor(canvas, ctx, options = {}) {
    this.canvas = canvas;
    this.ctx = ctx;

    // 获取Canvas的CSS尺寸（而不是实际像素尺寸）
    const dpr = wx.getSystemInfoSync().pixelRatio;
    this.width = canvas.width / dpr;
    this.height = canvas.height / dpr;
    
    // 默认配置
    this.options = {
      padding: {
        top: 40,
        right: 40,
        bottom: 60,
        left: 60
      },
      colors: {
        practiceCount: '#667eea',
        accuracy: '#4CAF50',
        totalTime: '#FF9800',
        grid: '#E0E0E0',
        text: '#666666',
        background: '#FFFFFF'
      },
      lineWidth: 3,
      pointRadius: 6,
      fontSize: 12,
      showGrid: true,
      showPoints: true,
      showLabels: true,
      ...options
    };
    
    this.chartArea = {
      x: this.options.padding.left,
      y: this.options.padding.top,
      width: this.width - this.options.padding.left - this.options.padding.right,
      height: this.height - this.options.padding.top - this.options.padding.bottom
    };
    
    // 当前显示的数据类型
    this.currentDataType = 'practiceCount'; // practiceCount, accuracy, totalTime
    this.data = null;
    this.touchInfo = null;
  }
  
  /**
   * 绘制趋势图
   * @param {Object} data - 图表数据
   * @param {string} dataType - 数据类型
   */
  drawChart(data, dataType = 'practiceCount') {
    this.data = data;
    this.currentDataType = dataType;

    console.log('[TrendChart] 绘制图表:', {
      dataType: dataType,
      values: data[dataType],
      maxValue: this.getMaxValue(),
      labels: data.labels,
      canvasSize: { width: this.width, height: this.height },
      chartArea: this.chartArea
    });

    // 清空画布
    this.clearCanvas();

    // 绘制背景
    this.drawBackground();

    // 绘制网格
    if (this.options.showGrid) {
      this.drawGrid();
    }

    // 绘制坐标轴
    this.drawAxes();

    // 绘制数据线
    this.drawDataLine();

    // 绘制数据点
    if (this.options.showPoints) {
      this.drawDataPoints();
    }

    // 绘制标签
    if (this.options.showLabels) {
      this.drawLabels();
    }

    // 绘制图例
    this.drawLegend();
  }
  
  /**
   * 清空画布
   */
  clearCanvas() {
    this.ctx.clearRect(0, 0, this.width, this.height);
  }
  
  /**
   * 绘制背景
   */
  drawBackground() {
    this.ctx.fillStyle = this.options.colors.background;
    this.ctx.fillRect(0, 0, this.width, this.height);
  }
  
  /**
   * 绘制网格
   */
  drawGrid() {
    const { ctx, chartArea, options } = this;
    const { labels } = this.data;
    
    ctx.strokeStyle = options.colors.grid;
    ctx.lineWidth = 1;
    ctx.setLineDash([2, 2]);
    
    // 垂直网格线
    const stepX = chartArea.width / (labels.length - 1);
    for (let i = 0; i < labels.length; i++) {
      const x = chartArea.x + i * stepX;
      ctx.beginPath();
      ctx.moveTo(x, chartArea.y);
      ctx.lineTo(x, chartArea.y + chartArea.height);
      ctx.stroke();
    }
    
    // 水平网格线
    const gridLines = 5;
    const stepY = chartArea.height / gridLines;
    for (let i = 0; i <= gridLines; i++) {
      const y = chartArea.y + i * stepY;
      ctx.beginPath();
      ctx.moveTo(chartArea.x, y);
      ctx.lineTo(chartArea.x + chartArea.width, y);
      ctx.stroke();
    }
    
    ctx.setLineDash([]);
  }
  
  /**
   * 绘制坐标轴
   */
  drawAxes() {
    const { ctx, chartArea, options } = this;
    
    ctx.strokeStyle = options.colors.text;
    ctx.lineWidth = 2;
    
    // X轴
    ctx.beginPath();
    ctx.moveTo(chartArea.x, chartArea.y + chartArea.height);
    ctx.lineTo(chartArea.x + chartArea.width, chartArea.y + chartArea.height);
    ctx.stroke();
    
    // Y轴
    ctx.beginPath();
    ctx.moveTo(chartArea.x, chartArea.y);
    ctx.lineTo(chartArea.x, chartArea.y + chartArea.height);
    ctx.stroke();
  }
  
  /**
   * 绘制数据线
   */
  drawDataLine() {
    const { ctx, chartArea, options, data, currentDataType } = this;
    const values = data[currentDataType];
    const maxValue = this.getMaxValue();

    if (values.length === 0) return;

    // 检查是否所有数据都为0
    const hasData = values.some(value => value > 0);
    if (!hasData && currentDataType !== 'accuracy') {
      // 如果没有数据，绘制一条基线
      ctx.strokeStyle = '#E0E0E0';
      ctx.lineWidth = 1;
      ctx.setLineDash([5, 5]);

      const baselineY = chartArea.y + chartArea.height - 10;
      ctx.beginPath();
      ctx.moveTo(chartArea.x, baselineY);
      ctx.lineTo(chartArea.x + chartArea.width, baselineY);
      ctx.stroke();
      ctx.setLineDash([]);
      return;
    }

    ctx.strokeStyle = options.colors[currentDataType];
    ctx.lineWidth = options.lineWidth;
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';

    const stepX = chartArea.width / (values.length - 1);

    ctx.beginPath();

    for (let i = 0; i < values.length; i++) {
      const x = chartArea.x + i * stepX;
      let y;

      if (maxValue === 0 || maxValue === 1) {
        // 如果最大值很小，使用固定的高度显示
        y = values[i] > 0 ? chartArea.y + chartArea.height * 0.3 : chartArea.y + chartArea.height;
      } else {
        y = chartArea.y + chartArea.height - (values[i] / maxValue) * chartArea.height;
      }

      if (i === 0) {
        ctx.moveTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }
    }

    ctx.stroke();
  }
  
  /**
   * 绘制数据点
   */
  drawDataPoints() {
    const { ctx, chartArea, options, data, currentDataType } = this;
    const values = data[currentDataType];
    const maxValue = this.getMaxValue();

    // 检查是否有数据
    const hasData = values.some(value => value > 0);
    if (!hasData && currentDataType !== 'accuracy') {
      return; // 没有数据时不绘制点
    }

    ctx.fillStyle = options.colors[currentDataType];

    const stepX = chartArea.width / (values.length - 1);

    for (let i = 0; i < values.length; i++) {
      const x = chartArea.x + i * stepX;
      let y;

      if (maxValue === 0 || maxValue === 1) {
        // 如果最大值很小，使用固定的高度显示
        y = values[i] > 0 ? chartArea.y + chartArea.height * 0.3 : chartArea.y + chartArea.height;
      } else {
        y = chartArea.y + chartArea.height - (values[i] / maxValue) * chartArea.height;
      }

      // 调试信息
      if (currentDataType === 'accuracy' && values[i] > 0) {
        console.log('[TrendChart] 绘制正确率点:', {
          index: i,
          value: values[i],
          maxValue: maxValue,
          x: x,
          y: y,
          chartHeight: chartArea.height,
          percentage: (values[i] / maxValue) * 100
        });
      }

      // 只绘制有数据的点，或者正确率数据
      if (values[i] > 0 || currentDataType === 'accuracy') {
        ctx.beginPath();
        ctx.arc(x, y, options.pointRadius, 0, 2 * Math.PI);
        ctx.fill();

        // 添加白色边框
        ctx.strokeStyle = '#FFFFFF';
        ctx.lineWidth = 2;
        ctx.stroke();

        // 如果数据点很少，添加数值标签
        if (values.filter(v => v > 0).length <= 3 && values[i] > 0) {
          ctx.fillStyle = options.colors.text;
          ctx.font = `${options.fontSize - 2}px Arial`;
          ctx.textAlign = 'center';
          ctx.fillText(values[i].toString(), x, y - options.pointRadius - 5);
          ctx.fillStyle = options.colors[currentDataType]; // 恢复颜色
        }
      }
    }
  }
  
  /**
   * 绘制标签
   */
  drawLabels() {
    const { ctx, chartArea, options, data } = this;
    
    ctx.fillStyle = options.colors.text;
    ctx.font = `${options.fontSize}px Arial`;
    ctx.textAlign = 'center';
    
    // X轴标签
    const stepX = chartArea.width / (data.labels.length - 1);
    for (let i = 0; i < data.labels.length; i++) {
      const x = chartArea.x + i * stepX;
      const y = chartArea.y + chartArea.height + 20;
      ctx.fillText(data.labels[i], x, y);
    }
    
    // Y轴标签
    const maxValue = this.getMaxValue();
    const gridLines = 5;
    const stepY = chartArea.height / gridLines;
    const stepValue = maxValue / gridLines;
    
    ctx.textAlign = 'right';
    for (let i = 0; i <= gridLines; i++) {
      const y = chartArea.y + chartArea.height - i * stepY + 4;
      const value = Math.round(i * stepValue);
      ctx.fillText(value.toString(), chartArea.x - 10, y);
    }
  }
  
  /**
   * 绘制图例
   */
  drawLegend() {
    const { ctx, options, currentDataType } = this;
    
    const legendText = this.getLegendText();
    const legendColor = options.colors[currentDataType];
    
    ctx.fillStyle = legendColor;
    ctx.fillRect(20, 20, 12, 12);
    
    ctx.fillStyle = options.colors.text;
    ctx.font = `${options.fontSize}px Arial`;
    ctx.textAlign = 'left';
    ctx.fillText(legendText, 40, 31);
  }
  
  /**
   * 获取当前数据类型的最大值
   */
  getMaxValue() {
    const { data, currentDataType } = this;
    
    if (currentDataType === 'accuracy') {
      return 100; // 正确率最大值固定为100
    }
    
    const maxValue = data.maxValues[currentDataType];
    return maxValue > 0 ? maxValue : 1; // 避免除零
  }
  
  /**
   * 获取图例文本
   */
  getLegendText() {
    const typeMap = {
      practiceCount: '练习次数',
      accuracy: '正确率 (%)',
      totalTime: '练习时间 (分钟)'
    };
    
    return typeMap[this.currentDataType] || '数据';
  }
  
  /**
   * 处理触摸事件
   * @param {number} x - 触摸点X坐标
   * @param {number} y - 触摸点Y坐标
   * @returns {Object|null} 触摸点信息
   */
  handleTouch(x, y) {
    const { chartArea, data } = this;
    
    // 检查是否在图表区域内
    if (x < chartArea.x || x > chartArea.x + chartArea.width ||
        y < chartArea.y || y > chartArea.y + chartArea.height) {
      return null;
    }
    
    // 计算最近的数据点
    const stepX = chartArea.width / (data.labels.length - 1);
    const index = Math.round((x - chartArea.x) / stepX);
    
    if (index >= 0 && index < data.labels.length) {
      const values = data[this.currentDataType];
      return {
        index,
        label: data.labels[index],
        value: values[index],
        dataType: this.currentDataType,
        x: chartArea.x + index * stepX,
        y: chartArea.y + chartArea.height - (values[index] / this.getMaxValue()) * chartArea.height
      };
    }
    
    return null;
  }
  
  /**
   * 绘制触摸提示
   * @param {Object} touchInfo - 触摸点信息
   */
  drawTooltip(touchInfo) {
    if (!touchInfo) return;
    
    const { ctx, options } = this;
    const { x, y, label, value, dataType } = touchInfo;
    
    // 绘制提示点
    ctx.fillStyle = options.colors[dataType];
    ctx.beginPath();
    ctx.arc(x, y, options.pointRadius + 2, 0, 2 * Math.PI);
    ctx.fill();
    
    // 绘制提示框
    const text = `${label}: ${value}${dataType === 'accuracy' ? '%' : ''}`;
    const textWidth = ctx.measureText(text).width;
    const tooltipWidth = textWidth + 20;
    const tooltipHeight = 30;
    
    let tooltipX = x - tooltipWidth / 2;
    let tooltipY = y - tooltipHeight - 10;
    
    // 边界检查
    if (tooltipX < 10) tooltipX = 10;
    if (tooltipX + tooltipWidth > this.width - 10) tooltipX = this.width - tooltipWidth - 10;
    if (tooltipY < 10) tooltipY = y + 20;
    
    // 绘制提示框背景
    ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
    ctx.fillRect(tooltipX, tooltipY, tooltipWidth, tooltipHeight);
    
    // 绘制提示文本
    ctx.fillStyle = '#FFFFFF';
    ctx.font = `${options.fontSize}px Arial`;
    ctx.textAlign = 'center';
    ctx.fillText(text, tooltipX + tooltipWidth / 2, tooltipY + tooltipHeight / 2 + 4);
  }
  
  /**
   * 切换数据类型
   * @param {string} dataType - 新的数据类型
   */
  switchDataType(dataType) {
    if (this.data && this.data[dataType]) {
      this.drawChart(this.data, dataType);
    }
  }

  /**
   * 获取数据类型选项
   */
  static getDataTypeOptions() {
    return [
      { key: 'practiceCount', label: '练习次数', color: '#667eea' },
      { key: 'accuracy', label: '正确率', color: '#4CAF50' },
      { key: 'totalTime', label: '练习时间', color: '#FF9800' }
    ];
  }

  /**
   * 格式化数值显示
   * @param {number} value - 数值
   * @param {string} dataType - 数据类型
   * @returns {string} 格式化后的字符串
   */
  static formatValue(value, dataType) {
    switch (dataType) {
      case 'accuracy':
        return `${value}%`;
      case 'totalTime':
        return `${value}分钟`;
      case 'practiceCount':
      default:
        return value.toString();
    }
  }

  /**
   * 获取数据类型单位
   * @param {string} dataType - 数据类型
   * @returns {string} 单位字符串
   */
  static getDataTypeUnit(dataType) {
    const unitMap = {
      practiceCount: '次',
      accuracy: '%',
      totalTime: '分钟'
    };
    return unitMap[dataType] || '';
  }

  /**
   * 创建图表交互管理器
   * @param {Object} canvas - Canvas元素
   * @param {Object} chart - TrendChart实例
   * @param {Function} onTouch - 触摸回调函数
   * @returns {Object} 交互管理器
   */
  static createInteractionManager(canvas, chart, onTouch) {
    let isInteracting = false;
    let currentTouchInfo = null;

    const handleTouchStart = (e) => {
      isInteracting = true;
      const touch = e.touches[0];
      const rect = canvas.getBoundingClientRect();
      const x = touch.clientX - rect.left;
      const y = touch.clientY - rect.top;

      currentTouchInfo = chart.handleTouch(x, y);
      if (currentTouchInfo && onTouch) {
        onTouch(currentTouchInfo, 'start');
      }
    };

    const handleTouchMove = (e) => {
      if (!isInteracting) return;

      const touch = e.touches[0];
      const rect = canvas.getBoundingClientRect();
      const x = touch.clientX - rect.left;
      const y = touch.clientY - rect.top;

      currentTouchInfo = chart.handleTouch(x, y);
      if (currentTouchInfo && onTouch) {
        onTouch(currentTouchInfo, 'move');
      }
    };

    const handleTouchEnd = (e) => {
      isInteracting = false;
      if (onTouch) {
        onTouch(null, 'end');
      }
      currentTouchInfo = null;
    };

    return {
      bind() {
        canvas.addEventListener('touchstart', handleTouchStart);
        canvas.addEventListener('touchmove', handleTouchMove);
        canvas.addEventListener('touchend', handleTouchEnd);
        canvas.addEventListener('touchcancel', handleTouchEnd);
      },

      unbind() {
        canvas.removeEventListener('touchstart', handleTouchStart);
        canvas.removeEventListener('touchmove', handleTouchMove);
        canvas.removeEventListener('touchend', handleTouchEnd);
        canvas.removeEventListener('touchcancel', handleTouchEnd);
      },

      getCurrentTouchInfo() {
        return currentTouchInfo;
      }
    };
  }
}

module.exports = TrendChart;
