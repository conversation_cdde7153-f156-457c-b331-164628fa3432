/* 学习进度与分析页面样式 */

.page-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 页面头部 */
.header {
  background: linear-gradient(to bottom, #594995 0%, var(--primary-light) 100%);
  padding: 40rpx 40rpx 30rpx;
  color: white;
  text-align: center;
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
}

/* 内容区域 */
.content {
  padding-top: 260rpx; /* 预留头部高度，可根据实际调整 */
  padding-left: 30rpx;
  padding-right: 30rpx;
  padding-bottom: 80rpx;
}

/* 通用section样式 */
.section {
  margin-bottom: 40rpx;
}

.section.auth-watchers {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #303133;
  margin-bottom: 24rpx;
}

.section-title .textbook-info {
  display: block;
  font-size: 24rpx;
  font-weight: 400;
  color: #909399;
  margin-top: 8rpx;
}

.binding-list {
  display: flex;
  flex-wrap: nowrap;
  gap: 24rpx;
  justify-content: flex-start;
  overflow-x: auto;
  padding-bottom: 20rpx; /* for scrollbar */
  -webkit-overflow-scrolling: touch;
}

.binding-item {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 180rpx;
  height: 220rpx;
  margin: 10rpx;
  background-color: #f7f8fa;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.binding-item .avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  margin-bottom: 16rpx;
}

.binding-item .nickname {
  font-size: 26rpx;
  color: #333;
}

.unbind-btn {
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  width: 40rpx;
  height: 40rpx;
  background-color: #e64340;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  line-height: 1;
  cursor: pointer;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

.add-binding {
  background-color: #fff;
  border: 2rpx dashed #dcdfe6;
  box-shadow: none;
}

.add-binding .share-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background-color: transparent;
  padding: 0;
  margin: 0;
  border: none;
  line-height: normal;
}

.share-button::after {
  border: none;
}

.add-binding .add-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 16rpx;
}

.add-binding .add-text {
  font-size: 26rpx;
  color: #888;
}

/* 学习数据区域 */
.progress-data {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

/* 数据卡片 */
.data-card {
  background: #fafbfc;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  border: 1rpx solid #e9ecef;
}

.data-card.core-stats {
  background: transparent;
  padding: 0;
  border: none;
  box-shadow: none;
}

.data-card:last-child {
  margin-bottom: 0;
}

.card-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid #eee;
}

/* 核心数据网格 */
.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.stat-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background: white;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.stat-icon {
  font-size: 48rpx;
  margin-right: 16rpx;
}

.stat-data {
  flex: 1;
}

.stat-value {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: var(--primary-color, #667eea);
  margin-bottom: 4rpx;
  line-height: 1;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
  margin-top: 4rpx;
}

/* 图表占位符 */
.chart-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200rpx;
  background: white;
  border-radius: 12rpx;
  border: 2rpx dashed #ddd;
}

.placeholder-icon {
  font-size: 48rpx;
  margin-bottom: 16rpx;
}

.placeholder-text {
  font-size: 24rpx;
  color: #999;
}

/* 错题分析 */
.analysis-summary {
  color: #666;
  font-size: 26rpx;
  margin-top: 10rpx;
  margin-bottom: 20rpx;
}

.analysis-content {
  height: 300rpx; /* 固定高度 */
  overflow-y: auto; /* 添加滚动条 */
  border-radius: 12rpx;
  background: #fafafa;
  padding: 20rpx;
}

/* 错误字词网格布局 */
.error-words-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.error-words-grid .word-grid-item {
  position: relative;
  min-width: 114rpx;
  width: auto;
  max-width: 380rpx;
  height: 170rpx;
  background: white;
  border-radius: 12rpx;
  border: 2rpx solid #e0e0e0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
  transition: all 0.3s ease;
}

/* 根据字符数量调整宽度 */
.error-words-grid .word-grid-item[data-char-count="1"] {
  width: 114rpx;
}

.error-words-grid .word-grid-item[data-char-count="2"] {
  min-width: 180rpx;
  max-width: 220rpx;
}

.error-words-grid .word-grid-item[data-char-count="3"] {
  min-width: 220rpx;
  max-width: 260rpx;
}

.error-words-grid .word-grid-item[data-char-count="4"] {
  min-width: 260rpx;
  max-width: 300rpx;
}

.error-words-grid .word-grid-item[data-char-count="5"] {
  min-width: 300rpx;
  max-width: 340rpx;
}

.error-words-grid .word-grid-item[data-char-count="6"],
.error-words-grid .word-grid-item[data-char-count="7"],
.error-words-grid .word-grid-item[data-char-count="8"] {
  min-width: 340rpx;
  max-width: 380rpx;
}

/* 田字格容器 */
.word-grid-container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 16rpx;
  padding-top: 32rpx; /* 为拼音留出空间 */
  gap: 8rpx; /* 字符间的间距 */
}

/* 田字格样式 */
.tianzige {
  position: relative;
  width: 60rpx;
  height: 60rpx;
  margin: 0;
  background-color: #fff;
  border: 1px solid #ddd;
  flex-shrink: 0; /* 防止田字格被压缩 */
}

.tianzige::before,
.tianzige::after {
  content: '';
  position: absolute;
  background-color: #f0f0f0;
}

/* 横线 */
.tianzige::before {
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  transform: translateY(-50%);
}

/* 竖线 */
.tianzige::after {
  left: 50%;
  top: 0;
  bottom: 0;
  width: 1px;
  transform: translateX(-50%);
}

.tianzige-inner {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  z-index: 2;
}

/* 拼音样式 */
.character-pinyin {
  position: absolute;
  top: -28rpx;
  left: 50%;
  transform: translateX(-50%);
  font-size: 18rpx;
  color: #666;
  white-space: nowrap;
  z-index: 3;
}

/* 订正状态标记 */
.word-corrected {
  position: absolute;
  top: 4rpx;
  right: 4rpx;
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  background: #4caf50;
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 16rpx;
  opacity: 0.9;
  z-index: 5;
}

.word-pending {
  position: absolute;
  top: 4rpx;
  right: 4rpx;
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  background: #ff9800;
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 16rpx;
  opacity: 0.9;
  z-index: 5;
}

/* 错误次数信息 */
.error-count {
  position: absolute;
  bottom: 6rpx;
  left: 50%;
  transform: translateX(-50%);
  font-size: 16rpx;
  color: #ff9800;
  background: rgba(255, 255, 255, 0.9);
  padding: 2rpx 6rpx;
  border-radius: 8rpx;
  white-space: nowrap;
  z-index: 4;
}

/* 小屏幕设备自适应 */
@media (max-width: 750rpx) {
  .error-words-grid {
    gap: 12rpx;
  }

  .error-words-grid .word-grid-item[data-char-count="2"] {
    min-width: 160rpx;
    max-width: 200rpx;
  }

  .error-words-grid .word-grid-item[data-char-count="3"] {
    min-width: 200rpx;
    max-width: 220rpx;
  }

  .error-words-grid .word-grid-item[data-char-count="4"] {
    min-width: 220rpx;
    max-width: 240rpx;
  }

  .error-words-grid .word-grid-item[data-char-count="5"] {
    min-width: 240rpx;
    max-width: 260rpx;
  }

  .error-words-grid .word-grid-item[data-char-count="6"],
  .error-words-grid .word-grid-item[data-char-count="7"],
  .error-words-grid .word-grid-item[data-char-count="8"] {
    min-width: 260rpx;
    max-width: 280rpx;
  }

  .tianzige {
    width: 50rpx;
    height: 50rpx;
  }

  .tianzige-inner {
    font-size: 28rpx;
  }

  .character-pinyin {
    font-size: 16rpx;
    top: -24rpx;
  }

  .word-corrected,
  .word-pending {
    width: 20rpx;
    height: 20rpx;
    font-size: 14rpx;
  }

  .error-count {
    font-size: 14rpx;
    bottom: 4rpx;
  }
}

/* 学习建议 */
.suggestions-content {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.suggestion-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 20rpx;
  background: white;
  border-radius: 12rpx;
  border-left: 4rpx solid #667eea;
}

.suggestion-icon {
  font-size: 32rpx;
  min-width: 40rpx;
}

.suggestion-text {
  font-size: 26rpx;
  color: #333;
  line-height: 1.4;
}

/* 加载状态 */
.loading-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-content {
  background: white;
  padding: 40rpx 60rpx;
  border-radius: 16rpx;
  text-align: center;
}

.loading-text {
  font-size: 28rpx;
  color: #333;
}

/* 响应式适配 */
@media (max-width: 400px) {
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 20rpx;
  }
}

/* 按钮样式优化 */
button.wx-button {
  border-radius: 20rpx;
  font-size: 24rpx;
}

button.wx-button[type="primary"] {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
}

button.wx-button[type="warn"] {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  border: none;
}

button.wx-button[type="default"] {
  background: linear-gradient(135deg, #f1f2f6 0%, #ddd 100%);
  color: #333;
  border: none;
} 

/* 顶部查看对象选择器 - 固定 */
.view-selector-header.fixed-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background: linear-gradient(to bottom, #594995 0%, var(--primary-light) 100%);
  padding: 40rpx 30rpx 30rpx;
  padding-top: calc(30rpx + env(safe-area-inset-top));
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  color: white;
}
 

/* 查看对象选择器内部样式优化 */
.view-selector .section-title {
  color: white;
}

.view-selector .selector-container {
  width: 100%;
  background: rgba(255, 255, 255, 0.2);
  padding: 0; /* moved padding to inner view */
  border-radius: 12rpx;
}

.selector-content {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 16rpx 20rpx;
  width: 100%;
}

.selector-label,
.picker-text {
  font-size: 28rpx;
  font-weight: 500;
}

.picker-arrow {
  margin-left: 8rpx;
  font-size: 24rpx;
}

/* 移除 button 默认样式，避免在 add-binding 卡片中出现默认灰色背景 */
button.share-button {
  background: transparent;
  border: none;
  padding: 0;
}

button.share-button::after {
  border: none;
}

/* 趋势图样式 */
.trend-chart {
  padding: 20rpx 10rpx 30rpx 10rpx !important; /* 减少左右padding */
}

.trend-chart .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid #eee;
  padding-left: 20rpx;
  padding-right: 20rpx;
}

.chart-controls {
  display: flex;
  align-items: center;
}

.data-type-selector {
  display: flex;
  gap: 16rpx;
}

.data-type-option {
  font-size: 22rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  background: #f5f5f5;
  color: #999;
  transition: all 0.3s ease;
  cursor: pointer;
}

.data-type-option.active {
  background: rgba(102, 126, 234, 0.1);
  font-weight: 600;
}

.chart-container {
  position: relative;
  width: 100%;
  height: 500rpx;  /* 从400rpx增加到500rpx */
  background: white;
  border-radius: 12rpx;
  overflow: hidden;
}

.trend-canvas {
  width: 100%;
  height: 100%;
  display: block;
}

.chart-tooltip {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 12rpx 16rpx;
  border-radius: 8rpx;
  font-size: 22rpx;
  z-index: 10;
}

.tooltip-label {
  display: block;
  margin-bottom: 4rpx;
  opacity: 0.8;
}

.tooltip-value {
  display: block;
  font-weight: 600;
  font-size: 24rpx;
}

.chart-loading,
.chart-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300rpx;
  background: white;
  border-radius: 12rpx;
}

.loading-icon,
.empty-icon {
  font-size: 48rpx;
  margin-bottom: 16rpx;
}

.loading-text,
.empty-text {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.empty-hint {
  font-size: 22rpx;
  color: #999;
}

.chart-summary {
  display: flex;
  justify-content: space-around;
  margin: 20rpx 10rpx 0 10rpx; /* 减少左右margin */
  padding: 20rpx;
  background: white;
  border-radius: 12rpx;
  border: 1rpx solid #f0f0f0;
}

.summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.summary-label {
  font-size: 22rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.summary-value {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}