<!--学习进度与分析页面-->
<view class="page-container">
  <!-- 顶部固定查看对象选择器 -->
  <view class="view-selector-header fixed-header">
    <view class="section view-selector">
      <view class="section-title">查看对象</view>
      <picker class="selector-container" bindchange="onViewUserChange" range="{{viewUserOptions}}" range-key="name" hover-class="none">
        <view class="selector-content">
          <text class="selector-label">当前查看:</text>
          <view class="picker-display">
            <text class="picker-text">{{currentViewUser.nickname || '本人'}}</text>
            <text class="picker-arrow">▼</text>
          </view>
        </view>
      </picker>
    </view>
  </view>

  <!-- 内容区域 -->
  <view class="content">
    <!-- 我授权的观看者 -->
    <view class="section auth-watchers" wx:if="{{isViewingSelf}}">
      <view class="section-title">我授权的观看者 (可解绑)</view>
      <button type="primary" size="mini" bindtap="handleTestBind" style="margin-bottom: 15rpx;">绑定测试账号</button>
      <view class="binding-list">
        <!-- 已绑定的用户 -->
        <block wx:for="{{authWatchers}}" wx:key="openid">
          <view class="binding-item">
            <image class="avatar" src="{{item.avatar || '/images/avatar.png'}}"></image>
            <view class="nickname">{{item.nickname}}</view>
            <view class="unbind-btn" bindtap="onUnbindUser" data-watcherid="{{item.openid}}">✕</view>
          </view>
        </block>

        <!-- 绑定空位 -->
        <block wx:for="{{bindingSlotsLeft}}" wx:key="*this">
          <view class="binding-item add-binding">
            <button class="share-button" open-type="share">
              <image class="add-icon" src="/images/icon_add.png"></image>
              <view class="add-text">去绑定</view>
            </button>
          </view>
        </block>
      </view>
    </view>

    <!-- 学习数据详情 -->
    <view class="section progress-data">
      <view class="section-title">
        学习数据详情
        <view class="textbook-info" wx:if="{{false}}">{{currentViewUserTextbookInfo}}</view>
      </view>
      
      <!-- 核心数据卡片 -->
      <view class="data-card core-stats">
        <view class="stats-grid">
          <view class="stat-item">
            <view class="stat-icon">📚</view>
            <view class="stat-data">
              <text class="stat-value">{{progressData.totalPracticeCount}}</text>
              <text class="stat-label">练习次数</text>
            </view>
          </view>
          <view class="stat-item">
            <view class="stat-icon">✏️</view>
            <view class="stat-data">
              <text class="stat-value">{{progressData.totalWordsPracticed}}</text>
              <text class="stat-label">练习字词</text>
            </view>
          </view>
          <view class="stat-item">
            <view class="stat-icon">⏰</view>
            <view class="stat-data">
              <text class="stat-value">{{progressData.totalTimeFormatted}}</text>
              <text class="stat-label">累计时长</text>
            </view>
          </view>
          <view class="stat-item">
            <view class="stat-icon">🎯</view>
            <view class="stat-data">
              <text class="stat-value">{{progressData.accuracyRate}}%</text>
              <text class="stat-label">正确率</text>
            </view>
          </view>
          <view class="stat-item">
            <view class="stat-icon">❌</view>
            <view class="stat-data">
              <text class="stat-value">{{progressData.pendingErrors}}</text>
              <text class="stat-label">待处理错题</text>
            </view>
          </view>
          <view class="stat-item">
            <view class="stat-icon">✅</view>
            <view class="stat-data">
              <text class="stat-value">{{progressData.correctedErrors}}</text>
              <text class="stat-label">已订正错题</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 进度趋势图 -->
      <view class="data-card trend-chart">
        <view class="card-header">
          <view class="card-title">进度趋势</view>
          <view class="chart-controls">
            <view class="data-type-selector">
              <view
                wx:for="{{trendChartOptions}}"
                wx:key="key"
                class="data-type-option {{trendChartDataType === item.key ? 'active' : ''}}"
                style="color: {{trendChartDataType === item.key ? item.color : '#999'}}"
                bind:tap="onTrendChartDataTypeChange"
                data-type="{{item.key}}"
              >
                {{item.label}}
              </view>
            </view>
          </view>
        </view>

        <view class="chart-container">
          <canvas
            type="2d"
            id="trendChart"
            class="trend-canvas"
            wx:if="{{!trendChartLoading && !trendChartEmpty}}"
            bind:touchstart="onTrendChartTouchStart"
            bind:touchmove="onTrendChartTouchMove"
            bind:touchend="onTrendChartTouchEnd"
            disable-scroll="true"
          ></canvas>

          <!-- 触摸提示信息 -->
          <view class="chart-tooltip" wx:if="{{trendChartTooltip.show}}">
            <text class="tooltip-label">{{trendChartTooltip.label}}</text>
            <text class="tooltip-value">{{trendChartTooltip.value}}</text>
          </view>
        </view>

        <view class="chart-loading" wx:if="{{trendChartLoading}}">
          <text class="loading-icon">📊</text>
          <text class="loading-text">正在生成趋势图...</text>
        </view>

        <view class="chart-empty" wx:if="{{!trendChartLoading && trendChartEmpty}}">
          <text class="empty-icon">📈</text>
          <text class="empty-text">暂无练习数据</text>
          <text class="empty-hint">开始练习后即可查看趋势图</text>
        </view>

        <!-- 趋势图统计摘要 -->
        <view class="chart-summary" wx:if="{{!trendChartLoading && !trendChartEmpty}}">
          <view class="summary-item">
            <text class="summary-label">总练习</text>
            <text class="summary-value">{{trendChartSummary.totalPractices}}次</text>
          </view>
          <view class="summary-item">
            <text class="summary-label">平均正确率</text>
            <text class="summary-value">{{trendChartSummary.averageAccuracy}}%</text>
          </view>
          <view class="summary-item">
            <text class="summary-label">活跃天数</text>
            <text class="summary-value">{{trendChartSummary.activeDays}}天</text>
          </view>
        </view>
      </view>

      <!-- 错题分析 -->
      <view class="data-card error-analysis" wx:if="{{progressData.errorWordsCount > 0}}">
        <view class="card-title">错题分析</view>
        <view class="analysis-summary">
          当前有 {{progressData.errorWordsCount}} 个错题需要复习
        </view>
        <view class="analysis-content">
          <!-- 错误字词田字格展示 -->
          <view class="error-words-grid">
            <view
              class="word-grid-item"
              wx:for="{{errorWordsDisplay}}"
              wx:key="id"
              wx:for-item="word"
              data-char-count="{{word.word.length}}"
            >
              <!-- 订正状态标记 -->
              <view class="{{word.corrected ? 'word-corrected' : 'word-pending'}}">
                {{word.corrected ? '✓' : word.errorCount || '1'}}
              </view>

              <!-- 田字格背景 -->
              <view class="word-background">
                <!-- 将字词拆分为单个字符，每个字符一个田字格 -->
                <view class="word-grid-container">
                  <view class="tianzige" wx:for="{{word.word}}" wx:key="index" wx:for-item="char" wx:if="{{char && char !== ' '}}">
                    <view class="tianzige-inner">{{char}}</view>
                    <!-- 拼音显示 -->
                    <view class="character-pinyin" wx:if="{{word.pinyinArray && index < word.pinyinArray.length}}">
                      {{word.pinyinArray[index] || ''}}
                    </view>
                  </view>
                </view>
              </view>

              <!-- 错误次数信息 -->
              <view class="error-count" wx:if="{{word.errorCount > 1}}">
                错误{{word.errorCount}}次
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 学习建议 -->
      <view class="data-card suggestions">
        <view class="card-title">学习建议</view>
        <view class="suggestions-content">
          <view class="suggestion-item" wx:if="{{progressData.accuracyRate < 80}}">
            <text class="suggestion-icon">💡</text>
            <text class="suggestion-text">正确率有待提高，建议加强基础练习</text>
          </view>
          <view class="suggestion-item" wx:if="{{progressData.errorWordsCount > 10}}">
            <text class="suggestion-icon">📝</text>
            <text class="suggestion-text">错题较多，建议重点复习错题本</text>
          </view>
          <view class="suggestion-item" wx:if="{{progressData.totalPracticeTime < 1800}}">
            <text class="suggestion-icon">⏰</text>
            <text class="suggestion-text">练习时间较短，建议增加练习时长</text>
          </view>
          <view class="suggestion-item" wx:if="{{progressData.accuracyRate >= 80 && progressData.errorWordsCount <= 5}}">
            <text class="suggestion-icon">🎉</text>
            <text class="suggestion-text">学习状态良好，继续保持！</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-mask" wx:if="{{isLoading}}">
    <view class="loading-content">
      <text class="loading-text">加载中...</text>
    </view>
  </view>
</view> 