/**
 * 趋势图数据处理模块
 * 用于从用户训练历史记录中提取和处理趋势图所需的数据
 */
const SyncManager = require('./syncManager.js');
const SimpleStorage = require('./simpleStorage');
const cloudApi = require('./cloudApi');

class TrendChartData {
  
  /**
   * 获取趋势图数据
   * @param {number} days - 显示的天数，默认7天
   * @param {string} targetUserId - 目标用户ID，如果为空则使用当前用户
   * @returns {Promise<Object>} 趋势图数据
   */
  static async getTrendData(days = 7, targetUserId = null) {
    try {
      // 获取练习历史记录
      const practiceHistory = await this.getPracticeHistory(targetUserId);

      if (practiceHistory.length === 0) {
        return this.getEmptyTrendData(days);
      }

      // 生成日期范围
      const dateRange = this.generateDateRange(days);

      // 按日期聚合数据
      const dailyData = this.aggregateDataByDate(practiceHistory, dateRange);

      // 生成趋势图数据
      const trendData = this.generateTrendData(dailyData, dateRange);

      return trendData;
      
    } catch (error) {
      return this.getEmptyTrendData(days);
    }
  }
  
  /**
   * 获取练习历史记录
   * @param {string} targetUserId - 目标用户ID
   * @returns {Promise<Array>} 练习历史记录
   */
  static async getPracticeHistory(targetUserId) {
    try {
      // 1. 首先检查本地是否有practiceHistoryBase数据需要同步
      await this.syncLocalDataToCloud();

      // 2. 从API获取最新的练习历史记录
      try {
        const apiResponse = await cloudApi.user.getPracticeHistory({
          limit: 100, // 获取最近100条记录
          showLoading: false
        });

        // 检查API响应结构（可能是 {result: {success, data}} 或直接 {success, data}）
        const responseData = apiResponse.result || apiResponse;

        if (responseData.success && responseData.data && Array.isArray(responseData.data)) {
          const apiRecords = responseData.data.map(record => this.convertApiRecord(record));

          if (apiRecords.length > 0) {
            return this.deduplicateAndSort(apiRecords);
          }
        }
      } catch (apiError) {
        // API获取失败，回退到本地数据
      }

      // 如果API获取失败或无数据，回退到本地数据
      const practiceHistory = SimpleStorage.getPracticeHistory() || [];
      const practiceRecords = SimpleStorage.getPracticeRecords() || [];

      // 合并两种数据源
      let allRecords = [...practiceHistory];

      // 如果practiceRecords有数据且格式不同，进行转换
      if (practiceRecords.length > 0) {
        const convertedRecords = practiceRecords.map(record => this.convertPracticeRecord(record));
        allRecords = allRecords.concat(convertedRecords);
      }

      // 去重并排序
      allRecords = this.deduplicateAndSort(allRecords);
      return allRecords;

    } catch (error) {
      return [];
    }
  }

  /**
   * 同步本地数据到云端
   */
  static async syncLocalDataToCloud() {
    try {
      // 获取本地practiceHistoryBase数据
      const practiceHistoryBase = SimpleStorage.getPracticeHistoryBase() || [];

      if (practiceHistoryBase.length === 0) {
        return; // 没有本地数据需要同步
      }

      // practiceHistoryBase中的数据都是需要同步的
      // 不需要判断lastModified，只要有数据就需要上传
      try {
        // 转换所有记录为API格式
        const apiRecords = practiceHistoryBase.map(record => this.convertLocalRecordToApi(record));

        // 批量上传练习历史数据
        const uploadResult = await this.uploadPracticeHistoryBatch(apiRecords);

        if (uploadResult.success) {
          // 上传成功后清空本地practiceHistoryBase
          SimpleStorage.setPracticeHistoryBase([]);

          // 更新同步时间戳
          SimpleStorage.setLastSyncTime(Date.now());
        }

      } catch (error) {
        // 批量上传失败，尝试逐个上传
        await this.uploadPracticeHistoryOneByOne(practiceHistoryBase);
      }

    } catch (error) {
      // 同步失败，继续执行后续逻辑
    }
  }

  /**
   * 标记记录为已同步
   * @param {Array} syncedRecords - 已同步的记录
   * @param {number} syncTime - 同步时间戳
   */
  static markRecordsAsSynced(syncedRecords, syncTime) {
    try {
      const practiceHistoryBase = SimpleStorage.getPracticeHistoryBase() || [];

      // 为已同步的记录添加同步标记
      syncedRecords.forEach(syncedRecord => {
        const index = practiceHistoryBase.findIndex(record =>
          this.isSameRecord(record, syncedRecord)
        );

        if (index >= 0) {
          practiceHistoryBase[index].lastSynced = syncTime;
          practiceHistoryBase[index].synced = true;
        }
      });

      // 更新本地存储
      SimpleStorage.setPracticeHistoryBase(practiceHistoryBase);

    } catch (error) {
      // 标记失败不影响主流程
    }
  }

  /**
   * 判断两个记录是否为同一条记录
   * @param {Object} record1 - 记录1
   * @param {Object} record2 - 记录2
   * @returns {boolean} 是否为同一条记录
   */
  static isSameRecord(record1, record2) {
    // 通过时间戳和课程ID来判断是否为同一条记录
    const time1 = new Date(record1.timestamp || 0).getTime();
    const time2 = new Date(record2.timestamp || 0).getTime();
    const course1 = record1.courseId || record1.course_id || '';
    const course2 = record2.courseId || record2.course_id || '';

    return Math.abs(time1 - time2) < 1000 && course1 === course2; // 1秒内的时间差认为是同一条记录
  }

  /**
   * 批量上传练习历史数据
   * @param {Array} apiRecords - API格式的记录数组
   * @returns {Promise<Object>} 上传结果
   */
  static async uploadPracticeHistoryBatch(apiRecords) {
    try {
      // TODO: 这里需要根据实际的API接口来实现
      // 示例：假设有一个批量保存的API
      // return await cloudApi.user.batchSavePracticeHistory({ records: apiRecords });

      // 临时方案：使用现有的用户更新API来保存练习历史
      // 注意：这可能不是最佳方案，建议添加专门的练习历史保存API
      return await SyncManager.uploadLocalData()

    } catch (error) {
      throw error;
    }
  }

  /**
   * 逐个上传练习历史数据（备用方案）
   * @param {Array} dataToSync - 需要同步的本地练习历史数据
   */
  static async uploadPracticeHistoryOneByOne(dataToSync) {
    try {
      let successCount = 0;
      const syncedRecords = [];

      for (const record of dataToSync) {
        try {
          const apiRecord = this.convertLocalRecordToApi(record);

          // 使用用户更新API逐个保存
          await cloudApi.user.update({
            practiceHistoryRecord: apiRecord,
            recordTimestamp: new Date().toISOString()
          });

          successCount++;
          syncedRecords.push(record);
        } catch (error) {
          // 单个记录上传失败，继续下一个
          continue;
        }
      }

      if (successCount > 0) {
        // 部分或全部上传成功，更新同步时间戳和标记
        const currentTime = Date.now();
        SimpleStorage.setLastSyncTime(currentTime);
        this.markRecordsAsSynced(syncedRecords, currentTime);
      }

    } catch (error) {
      // 逐个上传也失败，保留本地数据
    }
  }

  /**
   * 将本地记录转换为API格式
   * @param {Object} localRecord - 本地记录
   * @returns {Object} API格式的记录
   */
  static convertLocalRecordToApi(localRecord) {
    return {
      course_id: localRecord.courseId || localRecord.course_id || 'unknown',
      type: localRecord.type || 'normal_practice',
      timestamp: localRecord.timestamp || new Date().toISOString(),
      accuracy: localRecord.accuracy || localRecord.correct_rate || 0,
      correct_count: localRecord.correctCount || localRecord.correct_count || 0,
      total_count: localRecord.totalCount || localRecord.total_count || 0,
      practice_time: localRecord.practiceTime || localRecord.practice_time || 0
    };
  }

  /**
   * 转换API返回的练习记录格式
   * @param {Object} record - API返回的练习记录
   * @returns {Object} 标准化的练习记录
   */
  static convertApiRecord(record) {
    const converted = {
      timestamp: record.timestamp || new Date().toISOString(),
      accuracy: parseFloat(record.accuracy) || record.correct_rate || 0,
      correctCount: parseInt(record.correct_count) || record.correctCount || 0,
      totalCount: parseInt(record.total_count) || record.totalCount || 0,
      practiceTime: parseInt(record.practice_time) || record.practiceTime || record.duration || 0,
      type: record.type || 'normal_practice',
      courseId: record.course_id || record.courseId || 'unknown'
    };

    return converted;
  }

  /**
   * 转换练习记录格式
   * @param {Object} record - 原始练习记录
   * @returns {Object} 标准化的练习记录
   */
  static convertPracticeRecord(record) {
    return {
      timestamp: record.timestamp || record.date || new Date().toISOString(),
      accuracy: record.accuracy || (record.statistics ? record.statistics.accuracy : 0),
      correctCount: record.correctCount || (record.statistics ? record.statistics.correctCount : 0),
      totalCount: record.totalCount || (record.statistics ? record.statistics.totalCount : 0),
      practiceTime: record.practiceTime || record.duration || 0,
      type: record.type || 'normal_practice'
    };
  }
  
  /**
   * 去重并排序记录
   * @param {Array} records - 记录数组
   * @returns {Array} 去重排序后的记录
   */
  static deduplicateAndSort(records) {
    // 按时间戳去重
    const uniqueRecords = records.filter((record, index, self) => 
      index === self.findIndex(r => r.timestamp === record.timestamp)
    );
    
    // 按时间排序（最新的在前）
    return uniqueRecords.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
  }
  
  /**
   * 生成日期范围
   * @param {number} days - 天数
   * @returns {Array} 日期字符串数组
   */
  static generateDateRange(days) {
    const dates = [];
    const today = new Date();
    
    for (let i = days - 1; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(today.getDate() - i);
      dates.push(date.toISOString().split('T')[0]); // YYYY-MM-DD格式
    }
    
    return dates;
  }
  
  /**
   * 按日期聚合数据
   * @param {Array} records - 练习记录
   * @param {Array} dateRange - 日期范围
   * @returns {Object} 按日期聚合的数据
   */
  static aggregateDataByDate(records, dateRange) {
    const dailyData = {};
    
    // 初始化每一天的数据
    dateRange.forEach(date => {
      dailyData[date] = {
        date,
        practiceCount: 0,
        totalCorrect: 0,
        totalQuestions: 0,
        totalTime: 0,
        records: []
      };
    });
    
    // 聚合练习记录
    records.forEach(record => {
      const recordDate = new Date(record.timestamp).toISOString().split('T')[0];

      if (dailyData[recordDate]) {
        const dayData = dailyData[recordDate];
        dayData.practiceCount++;
        dayData.totalCorrect += record.correctCount || 0;
        dayData.totalQuestions += record.totalCount || 0;
        dayData.totalTime += record.practiceTime || 0;
        dayData.records.push(record);
      }
    });
    
    return dailyData;
  }
  
  /**
   * 生成趋势图数据
   * @param {Object} dailyData - 按日期聚合的数据
   * @param {Array} dateRange - 日期范围
   * @returns {Object} 趋势图数据
   */
  static generateTrendData(dailyData, dateRange) {
    const chartData = {
      labels: [],
      practiceCount: [],
      accuracy: [],
      totalTime: [],
      maxValues: {
        practiceCount: 0,
        accuracy: 100,
        totalTime: 0
      },
      summary: {
        totalPractices: 0,
        averageAccuracy: 0,
        totalTime: 0,
        activeDays: 0
      }
    };
    
    let totalAccuracy = 0;
    let validAccuracyDays = 0;
    
    dateRange.forEach(date => {
      const dayData = dailyData[date];
      
      // 生成标签（月/日格式）
      const dateObj = new Date(date);
      const label = `${dateObj.getMonth() + 1}/${dateObj.getDate()}`;
      chartData.labels.push(label);
      
      // 练习次数
      chartData.practiceCount.push(dayData.practiceCount);
      chartData.maxValues.practiceCount = Math.max(chartData.maxValues.practiceCount, dayData.practiceCount);
      
      // 正确率
      let accuracy = 0;
      if (dayData.totalQuestions > 0) {
        accuracy = Math.round((dayData.totalCorrect / dayData.totalQuestions) * 100);
        totalAccuracy += accuracy;
        validAccuracyDays++;
      }
      chartData.accuracy.push(accuracy);
      
      // 练习时间（使用秒为单位）
      const timeInSeconds = dayData.totalTime;
      chartData.totalTime.push(timeInSeconds);
      chartData.maxValues.totalTime = Math.max(chartData.maxValues.totalTime, timeInSeconds);
      
      // 统计汇总
      chartData.summary.totalPractices += dayData.practiceCount;
      chartData.summary.totalTime += dayData.totalTime;
      if (dayData.practiceCount > 0) {
        chartData.summary.activeDays++;
      }
    });
    
    // 计算平均正确率
    chartData.summary.averageAccuracy = validAccuracyDays > 0 ? 
      Math.round(totalAccuracy / validAccuracyDays) : 0;
    
    return chartData;
  }
  
  /**
   * 获取空的趋势图数据
   * @param {number} days - 天数
   * @returns {Object} 空的趋势图数据
   */
  static getEmptyTrendData(days) {
    const dateRange = this.generateDateRange(days);
    const chartData = {
      labels: [],
      practiceCount: [],
      accuracy: [],
      totalTime: [],
      maxValues: {
        practiceCount: 1,
        accuracy: 100,
        totalTime: 1
      },
      summary: {
        totalPractices: 0,
        averageAccuracy: 0,
        totalTime: 0,
        activeDays: 0
      }
    };
    
    dateRange.forEach(date => {
      const dateObj = new Date(date);
      const label = `${dateObj.getMonth() + 1}/${dateObj.getDate()}`;
      chartData.labels.push(label);
      chartData.practiceCount.push(0);
      chartData.accuracy.push(0);
      chartData.totalTime.push(0);
    });
    
    return chartData;
  }
  
  /**
   * 格式化时间显示
   * @param {number} seconds - 秒数
   * @returns {string} 格式化的时间字符串
   */
  static formatTime(seconds) {
    if (seconds < 60) {
      return `${seconds}秒`;
    } else if (seconds < 3600) {
      const minutes = Math.floor(seconds / 60);
      return `${minutes}分钟`;
    } else {
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      return `${hours}小时${minutes}分钟`;
    }
  }
}

module.exports = TrendChartData;
