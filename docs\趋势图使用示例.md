# 学习进度趋势图使用示例

## 快速开始

### 1. 基本使用

在学习进度页面中，趋势图会自动加载并显示用户的练习数据：

```javascript
// 页面加载时自动初始化
onReady() {
  this.initTrendChart();
}

// 数据加载完成后自动绘制
loadProgressData() {
  // ... 加载进度数据
  this.loadTrendChartData(); // 加载趋势图数据
}
```

### 2. 手动生成测试数据

如果当前没有练习数据，可以生成一些测试数据来查看效果：

```javascript
// 在控制台中运行以下代码生成测试数据
const TrendChartTest = require('./utils/trendChartTest');

// 生成并存储测试数据
const testData = TrendChartTest.generateTestData();
wx.setStorageSync('practiceHistory', testData);

// 重新加载页面查看效果
```

### 3. 数据格式示例

趋势图支持以下格式的练习记录：

```javascript
// 标准格式
{
  timestamp: "2025-01-15T10:30:00.000Z",
  accuracy: 85,
  correctCount: 8,
  totalCount: 10,
  practiceTime: 300, // 秒
  type: "normal_practice"
}

// 兼容格式
{
  date: "2025-01-15T10:30:00.000Z",
  statistics: {
    accuracy: 90,
    correctCount: 9,
    totalCount: 10
  },
  duration: 240
}
```

## 功能演示

### 1. 数据类型切换

用户可以在三种数据类型间切换：

```javascript
// 切换到练习次数视图
onTrendChartDataTypeChange(e) {
  const dataType = 'practiceCount';
  this.setData({ trendChartDataType: dataType });
  this.data.trendChart.drawChart(this.data.trendChartData, dataType);
}

// 切换到正确率视图
// dataType = 'accuracy'

// 切换到练习时间视图  
// dataType = 'totalTime'
```

### 2. 触摸交互

图表支持触摸交互，显示具体数值：

```javascript
// 触摸开始 - 显示提示
onTrendChartTouchStart(e) {
  const touch = e.touches[0];
  const touchInfo = this.data.trendChart.handleTouch(touch.x, touch.y);
  
  if (touchInfo) {
    this.setData({
      'trendChartTooltip.show': true,
      'trendChartTooltip.label': touchInfo.label,
      'trendChartTooltip.value': TrendChart.formatValue(touchInfo.value, touchInfo.dataType)
    });
  }
}

// 触摸结束 - 隐藏提示
onTrendChartTouchEnd(e) {
  this.setData({
    'trendChartTooltip.show': false
  });
}
```

### 3. 自定义配置

可以自定义图表的外观和行为：

```javascript
// 创建自定义配置的图表
const customChart = new TrendChart(canvas, ctx, {
  padding: { top: 40, right: 40, bottom: 60, left: 60 },
  colors: {
    practiceCount: '#FF6B6B',
    accuracy: '#4ECDC4', 
    totalTime: '#45B7D1',
    grid: '#F0F0F0',
    text: '#333333'
  },
  lineWidth: 4,
  pointRadius: 8,
  fontSize: 14
});
```

## 实际应用场景

### 1. 学习进度监控

家长可以通过趋势图了解孩子的学习情况：

```javascript
// 查看孩子的学习数据
const childUserId = 'child_openid';
const trendData = TrendChartData.getTrendData(7, childUserId);

console.log('孩子本周练习情况:', trendData.summary);
// 输出: { totalPractices: 15, averageAccuracy: 87, activeDays: 5 }
```

### 2. 学习习惯分析

通过趋势图可以分析学习习惯：

```javascript
// 分析学习规律
function analyzeLearningPattern(trendData) {
  const { practiceCount, accuracy, labels } = trendData;
  
  // 找出最活跃的学习日
  const maxPracticeIndex = practiceCount.indexOf(Math.max(...practiceCount));
  const mostActiveDay = labels[maxPracticeIndex];
  
  // 计算正确率趋势
  const accuracyTrend = accuracy.slice(-3).reduce((sum, acc) => sum + acc, 0) / 3;
  
  return {
    mostActiveDay,
    recentAccuracy: accuracyTrend,
    consistency: practiceCount.filter(count => count > 0).length
  };
}
```

### 3. 学习建议生成

基于趋势数据生成个性化建议：

```javascript
function generateLearningAdvice(trendData) {
  const { summary, accuracy } = trendData;
  const advice = [];
  
  // 练习频率建议
  if (summary.activeDays < 5) {
    advice.push('建议增加练习频率，保持每日练习习惯');
  }
  
  // 正确率建议
  const avgAccuracy = summary.averageAccuracy;
  if (avgAccuracy < 70) {
    advice.push('正确率偏低，建议复习基础知识');
  } else if (avgAccuracy > 90) {
    advice.push('正确率很高，可以尝试更有挑战性的内容');
  }
  
  // 学习时间建议
  if (summary.totalTime < 1800) { // 少于30分钟
    advice.push('建议适当增加学习时间');
  }
  
  return advice;
}
```

## 调试技巧

### 1. 数据验证

```javascript
// 检查数据完整性
function validateTrendData(data) {
  const required = ['labels', 'practiceCount', 'accuracy', 'totalTime'];
  const missing = required.filter(field => !data[field]);
  
  if (missing.length > 0) {
    console.error('缺少必要字段:', missing);
    return false;
  }
  
  console.log('数据验证通过');
  return true;
}
```

### 2. 性能监控

```javascript
// 监控绘制性能
function measureDrawPerformance(chart, data, dataType) {
  const startTime = performance.now();
  
  chart.drawChart(data, dataType);
  
  const endTime = performance.now();
  console.log(`图表绘制耗时: ${endTime - startTime}ms`);
}
```

### 3. 错误处理

```javascript
// 优雅处理错误
try {
  const trendData = TrendChartData.getTrendData(7);
  chart.drawChart(trendData, 'practiceCount');
} catch (error) {
  console.error('趋势图绘制失败:', error);
  
  // 显示错误状态
  this.setData({
    trendChartEmpty: true,
    trendChartLoading: false
  });
}
```

## 最佳实践

### 1. 数据更新策略

```javascript
// 增量更新而非全量重绘
updateTrendChart(newRecord) {
  // 添加新记录到本地存储
  const history = SimpleStorage.getPracticeHistory();
  history.unshift(newRecord);
  SimpleStorage.setPracticeHistory(history);
  
  // 重新计算趋势数据
  const trendData = TrendChartData.getTrendData(7);
  
  // 更新图表
  if (this.data.trendChart) {
    this.data.trendChart.drawChart(trendData, this.data.trendChartDataType);
  }
}
```

### 2. 内存管理

```javascript
// 页面卸载时清理资源
onUnload() {
  if (this.data.trendChart) {
    this.data.trendChart = null;
  }
  
  this.setData({
    trendChartCanvas: null,
    trendChartContext: null,
    trendChartData: null
  });
}
```

### 3. 用户体验优化

```javascript
// 加载状态管理
async loadTrendChartData() {
  this.setData({ trendChartLoading: true });
  
  try {
    const trendData = await TrendChartData.getTrendData(7);
    
    // 延迟一点时间，让用户看到加载过程
    setTimeout(() => {
      this.setData({
        trendChartData: trendData,
        trendChartLoading: false,
        trendChartEmpty: trendData.summary.totalPractices === 0
      });
      
      if (!this.data.trendChartEmpty && this.data.trendChart) {
        this.data.trendChart.drawChart(trendData, this.data.trendChartDataType);
      }
    }, 500);
    
  } catch (error) {
    this.setData({
      trendChartLoading: false,
      trendChartEmpty: true
    });
  }
}
```

---

通过以上示例，您可以充分利用学习进度趋势图功能，为用户提供直观、有用的学习数据可视化体验。
