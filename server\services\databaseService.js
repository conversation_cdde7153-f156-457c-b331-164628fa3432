const mysql = require('mysql2/promise');
const ObjectId = require('bson-objectid');
const config = require('../config.js');

class DatabaseService {
  static pool = null;
  static testMode = false;
  static memoryData = {
    users: new Map(),
    vipSubscriptions: new Map(),
    vipPrivileges: new Map(),
    vipOrders: new Map()
  };

  static formatISODateTime(date) {
    // 将日期转换为 MySQL DATETIME 字符串 `YYYY-MM-DD HH:MM:SS`
    if (!date) return null;
    let d = date;
    if (!(d instanceof Date)) {
      d = new Date(date);
      if (isNaN(d.getTime())) return null;
    }
    return d.toISOString().slice(0, 19).replace('T', ' ');
  }

  static async initializePool() {
    if (!this.pool) {
      try {
        this.pool = mysql.createPool({ ...config.database, charset: 'utf8mb4' });
        const connection = await this.pool.getConnection();
        await connection.ping();
        connection.release();
        console.log('✅ MySQL 数据库连接池初始化成功');
        await this.initializeTables();
        
        // **禁用测试模式，使用真实数据库**
        this.testMode = false;
        console.log('✅ 使用真实数据库模式');
        
      } catch (error) {
        console.warn('⚠️ MySQL 数据库连接失败，启用测试模式:', error.message);
        this.testMode = true;
        this.initializeTestData();
      }
    }
    return this.pool;
  }

  static initializeTestData() {
    console.log('🧪 初始化测试数据...');
    
    // 初始化VIP特权数据
    const privileges = [
      { id: 1, name: 'unlimited_practice', display_name: '无限练习', description: '不限制每日练习次数', is_active: true },
      { id: 2, name: 'advanced_statistics', display_name: '高级统计', description: '查看详细学习统计和进度分析', is_active: true },
      { id: 3, name: 'priority_support', display_name: '优先客服', description: '享受优先客服支持服务', is_active: true },
      { id: 4, name: 'exclusive_content', display_name: '专属内容', description: '访问VIP专属学习内容', is_active: true },
      { id: 5, name: 'offline_download', display_name: '离线下载', description: '下载内容离线学习', is_active: true }
    ];
    
    privileges.forEach(privilege => {
      this.memoryData.vipPrivileges.set(privilege.id, privilege);
    });
    
    // 添加测试VIP用户数据（方便调试VIP功能）
    const testVipUser = {
      user_id: 1,
      is_vip: true,
      vip_type: 'monthly',
      vip_start_time: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 7天前开始
      vip_end_time: new Date(Date.now() + 23 * 24 * 60 * 60 * 1000), // 23天后到期
      status: 'active',
      created_at: new Date(),
      updated_at: new Date()
    };
    
    this.memoryData.vipSubscriptions.set(1, testVipUser);
    
    console.log(`✅ 测试模式初始化完成，VIP特权数量: ${privileges.length}, 测试VIP用户: 1个`);
  }

  static async getConnection() {
    if (this.testMode) {
      return null; // 测试模式不需要连接
    }
    if (!this.pool) await this.initializePool();
    return await this.pool.getConnection();
  }

  static async query(sql, params = []) {
    if (this.testMode) {
      console.log('🧪 测试模式：模拟数据库查询');
      return this.handleTestModeQuery(sql, params);
    }
    const connection = await this.getConnection();
    try {
      const [rows] = await connection.execute(sql, params);
      return rows;
    } finally {
      connection.release();
    }
  }

  static handleTestModeQuery(sql, params = []) {
    // VIP状态查询
    if (sql.includes('user_vip_status') && sql.includes('SELECT')) {
      const userId = params[0] || 1;
      const vipRecord = this.memoryData.vipSubscriptions.get(userId);
      
      if (vipRecord) {
        const now = new Date();
        const isExpired = now > new Date(vipRecord.vip_end_time);
        
        return [{
          user_id: vipRecord.user_id,
          openid: 'test_openid',
          is_vip: vipRecord.is_vip && !isExpired,
          vip_type: vipRecord.vip_type,
          vip_start_time: vipRecord.vip_start_time,
          vip_end_time: vipRecord.vip_end_time,
          status: isExpired ? 'expired' : vipRecord.status,
          auto_renew: false,
          created_at: vipRecord.created_at,
          updated_at: vipRecord.created_at
        }];
      }
      
      return [{
        user_id: 1,
        openid: 'test_openid',
        is_vip: false,
        vip_type: null,
        vip_start_time: null,
        vip_end_time: null,
        status: 'free',
        auto_renew: false,
        created_at: new Date(),
        updated_at: new Date()
      }];
    }
    
    // VIP特权查询
    if (sql.includes('vip_privileges') && sql.includes('SELECT')) {
      const privileges = Array.from(this.memoryData.vipPrivileges.values());
      if (params.length > 0) {
        // 查询特定特权
        const privilegeKey = params[0];
        const privilege = privileges.find(p => p.name === privilegeKey);
        return privilege ? [privilege] : [];
      }
      // 返回所有特权
      return privileges.map(p => ({
        privilege_key: p.name,
        privilege_name: p.display_name,
        description: p.description,
        privilege_type: 'feature',
        free_user_limit: 0,
        vip_user_limit: 1,
        is_active: p.is_active
      }));
    }
    
    // 订单查询
    if (sql.includes('payment_orders') && sql.includes('SELECT')) {
      if (params.length > 0) {
        const orderId = params[0];
        const order = this.memoryData.vipOrders.get(orderId);
        return order ? [order] : [];
      }
      return Array.from(this.memoryData.vipOrders.values());
    }
    
    // 订单插入
    if (sql.includes('payment_orders') && sql.includes('INSERT')) {
      const orderRecord = {
        order_id: params[0],
        user_id: params[1],
        openid: params[2],
        vip_type: params[3],
        amount: params[4],
        status: params[5],
        prepay_id: params[6],
        created_at: new Date(),
        updated_at: new Date()
      };
      this.memoryData.vipOrders.set(params[0], orderRecord);
      return { insertId: 1, affectedRows: 1 };
    }
    
    // 订单更新
    if (sql.includes('payment_orders') && sql.includes('UPDATE')) {
      const orderId = params[params.length - 1]; // 最后一个参数是order_id
      const order = this.memoryData.vipOrders.get(orderId);
      if (order) {
        order.status = params[0];
        order.updated_at = new Date();
        this.memoryData.vipOrders.set(orderId, order);
      }
      return { affectedRows: 1 };
    }

    // 用户查询
    if (sql.includes('users') && sql.includes('SELECT')) {
      return [{
        id: 1,
        openid: 'test_openid',
        nickname: '测试用户',
        avatar: '/images/avatar.png'
      }];
    }
    
    // 默认返回空数组
    return [];
  }

  static async saveUser(userInfo) {
    if (this.testMode) {
      const { openid, nickname, avatar, userType = 'wechat' } = userInfo;
      const userId = this.memoryData.users.size + 1;
      const user = {
        id: userId,
        display_user_id: `U-test-${userId}`,
        openid,
        nickname: nickname || `测试用户${userId}`,
        avatar: avatar || '/images/avatar.png',
        user_type: userType,
        created_at: new Date(),
        updated_at: new Date()
      };
      
      this.memoryData.users.set(openid, user);
      
      return { 
        success: true, 
        user: { 
          _id: user.id,
          display_user_id: user.display_user_id,
          openid: user.openid, 
          createAt: user.created_at 
        } 
      };
    }

    const connection = await this.getConnection();
    try {
      await connection.beginTransaction();

      const { openid, nickname, avatar, userType = 'wechat' } = userInfo;
      const now = new Date();
      const nowFormatted = this.formatISODateTime(now);

      const isEmoji = (str) => {
        if (!str) return false;
        return /\p{Emoji}/u.test(str);
      };
      const finalAvatar = avatar && !isEmoji(avatar) ? avatar : '/images/avatar.png';

      let [existingUserRows] = await connection.execute('SELECT id, display_user_id FROM users WHERE openid = ?', [openid]);
      let userId;
      let displayUserId;

      if (existingUserRows.length > 0) {
        userId = existingUserRows[0].id;
        displayUserId = existingUserRows[0].display_user_id;
        await connection.execute(
          `UPDATE users SET nickname = ?, avatar = ?, user_type = ?, updated_at = ? WHERE id = ?`,
          [nickname || null, finalAvatar, userType, nowFormatted, userId]
        );
      } else {
        const [result] = await connection.execute(
          `INSERT INTO users (openid, nickname, avatar, user_type, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?)`,
          [openid, nickname || null, finalAvatar, userType, nowFormatted, nowFormatted]
        );
        userId = result.insertId;
        displayUserId = null; // 新用户，display_user_id 尚未生成
      }

      // 如果 display_user_id 不存在，则生成并更新它
      if (!displayUserId) {
        // 使用一个简单的、基于ID的、非连续的生成策略
        displayUserId = `U-${(userId * 3 + 10000).toString(16)}`;
        await connection.execute(
          'UPDATE users SET display_user_id = ? WHERE id = ?',
          [displayUserId, userId]
        );
      }

      await connection.commit();
      
      const [finalUser] = await connection.execute('SELECT * FROM users WHERE id = ?', [userId]);

      return { 
        success: true, 
        user: { 
          _id: finalUser[0].id,
          display_user_id: finalUser[0].display_user_id,
          openid: finalUser[0].openid, 
          createAt: finalUser[0].created_at 
        } 
      };
    } catch (error) {
      await connection.rollback();
      console.error('[DatabaseService] 保存用户失败:', error);
      return { success: false, error: error.message };
    } finally {
      connection.release();
    }
  }

  static async getUserByOpenid(openid) {
    if (this.testMode) {
      console.log('🧪 测试模式：获取用户信息');
      
      // 为不同的openid分配不同的用户ID
      let userId;
      if (!this.memoryData.openidToUserId) {
        this.memoryData.openidToUserId = new Map();
      }
      
      if (this.memoryData.openidToUserId.has(openid)) {
        userId = this.memoryData.openidToUserId.get(openid);
      } else {
        userId = this.memoryData.openidToUserId.size + 1;
        this.memoryData.openidToUserId.set(openid, userId);
      }
      
      const user = this.memoryData.users.get(openid);
      if (!user) {
        // 创建测试用户
        const testUser = {
          id: userId,
          display_user_id: `U-test-${userId}`,
          openid,
          nickname: `测试用户${userId}`,
          avatar: '/images/avatar.png',
          user_type: 'wechat',
          authWatchers: [],
          watchTargets: [],
          created_at: new Date(),
          updated_at: new Date()
        };
        this.memoryData.users.set(openid, testUser);
        
        // **修复：为测试用户添加绑定关系**
        if (userId === 1) {
          // 用户1可以观看用户2的数据
          testUser.watchTargets = ['oPFXx60E71qLuLPkByhcqx3rNf0E'];
        } else if (userId === 2) {
          // 用户2授权用户1观看
          testUser.authWatchers = ['oPFXx61CJ8Qf6b40j-f13Id5QEWk'];
        }
        
        return {
          _id: testUser.id,
          openid: testUser.openid,
          nickname: testUser.nickname,
          avatar: testUser.avatar,
          userType: testUser.user_type,
          authWatchers: testUser.authWatchers,
          watchTargets: testUser.watchTargets,
          createdAt: testUser.created_at,
          updatedAt: testUser.updated_at
        };
      }
      
      // **修复：为现有用户添加绑定关系**
      let authWatchers = user.authWatchers || [];
      let watchTargets = user.watchTargets || [];
      
      if (userId === 1) {
        // 用户1可以观看用户2的数据
        watchTargets = ['oPFXx60E71qLuLPkByhcqx3rNf0E'];
      } else if (userId === 2) {
        // 用户2授权用户1观看
        authWatchers = ['oPFXx61CJ8Qf6b40j-f13Id5QEWk'];
      }
      
      return {
        _id: user.id,
        openid: user.openid,
        nickname: user.nickname,
        avatar: user.avatar,
        userType: user.user_type,
        authWatchers: authWatchers,
        watchTargets: watchTargets,
        createdAt: user.created_at,
        updatedAt: user.updated_at,
        userData: user.userData
      };
    }

    try {
      const [user] = await this.query('SELECT * FROM users WHERE openid = ?', [openid]);
      if (!user) return null;
      
      const result = {
        _id: user.id,
        openid: user.openid,
        nickname: user.nickname,
        avatar: user.avatar,
        userType: user.user_type,
        authWatchers: Array.isArray(user.auth_watchers) ? user.auth_watchers : (typeof user.auth_watchers === 'string' && user.auth_watchers.startsWith('[') ? JSON.parse(user.auth_watchers) : []),
        watchTargets: Array.isArray(user.watch_targets) ? user.watch_targets : (typeof user.watch_targets === 'string' && user.watch_targets.startsWith('[') ? JSON.parse(user.watch_targets) : []),
        createdAt: user.created_at,
        updatedAt: user.updated_at,
        userData: user.userData
      };

      console.log('[DatabaseService] 获取用户信息成功:', openid);
      return result;

    } catch (error) {
      console.error('[DatabaseService] 获取用户信息失败:', error);
      throw error;
    }
  }

  /**
   * 更新用户绑定关系
   */
  static async updateUserBindings(currentOpenid, targetOpenid, action) {
    if (this.testMode) {
      // 在测试模式下模拟绑定操作
      return { success: true };
    }

    const connection = await this.getConnection();
    try {
      await connection.beginTransaction();

      // 获取两个用户的当前数据
      const [currentUserRows] = await connection.execute(
        'SELECT id, auth_watchers, watch_targets FROM users WHERE openid = ?', 
        [currentOpenid]
      );
      const [targetUserRows] = await connection.execute(
        'SELECT id, auth_watchers, watch_targets FROM users WHERE openid = ?', 
        [targetOpenid]
      );

      if (currentUserRows.length === 0 || targetUserRows.length === 0) {
        throw new Error('用户不存在');
      }

      const currentUser = currentUserRows[0];
      const targetUser = targetUserRows[0];

      // 解析当前的绑定关系
      const currentAuthWatchers = Array.isArray(currentUser.auth_watchers) ? currentUser.auth_watchers : (typeof currentUser.auth_watchers === 'string' && currentUser.auth_watchers.startsWith('[') ? JSON.parse(currentUser.auth_watchers) : []);
      const targetWatchTargets = Array.isArray(targetUser.watch_targets) ? targetUser.watch_targets : (typeof targetUser.watch_targets === 'string' && targetUser.watch_targets.startsWith('[') ? JSON.parse(targetUser.watch_targets) : []);

      let newCurrentAuthWatchers, newTargetWatchTargets;

      if (action === 'bind') {
        // 添加绑定关系
        newCurrentAuthWatchers = [...currentAuthWatchers, targetOpenid];
        newTargetWatchTargets = [...targetWatchTargets, currentOpenid];
      } else if (action === 'unbind') {
        // 移除绑定关系
        newCurrentAuthWatchers = currentAuthWatchers.filter(id => id !== targetOpenid);
        newTargetWatchTargets = targetWatchTargets.filter(id => id !== currentOpenid);
      } else {
        throw new Error('无效的操作类型');
      }

      // 更新数据库
      const now = this.formatISODateTime(new Date());
      
      await connection.execute(
        'UPDATE users SET auth_watchers = ?, updated_at = ? WHERE openid = ?',
        [JSON.stringify(newCurrentAuthWatchers), now, currentOpenid]
      );

      await connection.execute(
        'UPDATE users SET watch_targets = ?, updated_at = ? WHERE openid = ?',
        [JSON.stringify(newTargetWatchTargets), now, targetOpenid]
      );

      await connection.commit();
      
      console.log('[DatabaseService] 更新用户绑定关系成功:', { action, currentOpenid, targetOpenid });
      return { success: true };

    } catch (error) {
      await connection.rollback();
      console.error('[DatabaseService] 更新用户绑定关系失败:', error);
      return { success: false, error: error.message };
    } finally {
      connection.release();
    }
  }

  /**
   * 获取用户同步数据
   */
  static async getUserData(openid) {
    if (this.testMode) {
      // 返回模拟数据
      return {
        openid,
        user_data: JSON.stringify({
          totalPracticeCount: 100,
          totalCorrectCount: 80,
          totalWrongCount: 20,
          totalPracticeTime: 1200
        }),
        learning_progress: JSON.stringify({}),
        error_words: JSON.stringify({})
      };
    }

    try {
      const [userData] = await this.query(
        'SELECT * FROM user_data_sync WHERE openid = ?', 
        [openid]
      );
      
      if (!userData) {
        console.log('[DatabaseService] 用户数据不存在:', openid);
        return null;
      }

      console.log('[DatabaseService] 获取用户数据成功:', openid);
      return userData;

    } catch (error) {
      console.error('[DatabaseService] 获取用户数据失败:', error);
      throw error;
    }
  }

  static async initializeTables() {
    const connection = await this.getConnection();
    try {
      const createUsersTable = `
      CREATE TABLE IF NOT EXISTS users (
        id INT PRIMARY KEY AUTO_INCREMENT,
        display_user_id VARCHAR(50) UNIQUE,
        openid VARCHAR(255) NOT NULL UNIQUE,
        nickname VARCHAR(255),
        avatar VARCHAR(1024),
        user_type VARCHAR(50),
        auth_watchers JSON,
        watch_targets JSON,
        created_at DATETIME,
        updated_at DATETIME
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci`;

      const createUserDataSyncTable = `
      CREATE TABLE IF NOT EXISTS user_data_sync (
          user_id INT PRIMARY KEY,
          openid VARCHAR(255),
          user_data JSON,
          textbook_data JSON,
          current_textbook JSON,
          learning_progress JSON,
          daily_tasks_data JSON,
          last_modified DATETIME NOT NULL,
          update_time DATETIME NOT NULL
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci`;

      const createWordRewardsTable = `
        CREATE TABLE IF NOT EXISTS word_rewards (
          id INT PRIMARY KEY AUTO_INCREMENT,
          user_id INT NOT NULL,
          word VARCHAR(255) NOT NULL,
          course_id VARCHAR(100),
          has_points_reward BOOLEAN DEFAULT FALSE,
          has_exp_reward BOOLEAN DEFAULT FALSE,
          timestamp DATETIME,
          UNIQUE KEY unique_user_word_course_id (user_id, word, course_id),
          INDEX idx_user_id (user_id),
          INDEX idx_course_id (course_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci`;

      const createErrorWordsTable = `
        CREATE TABLE IF NOT EXISTS error_words (
          id VARCHAR(255) PRIMARY KEY,
          user_id INT NOT NULL,
          word VARCHAR(255) NOT NULL,
          course_id VARCHAR(100),
          timestamp DATETIME,
          corrected BOOLEAN DEFAULT FALSE,
          corrected_time DATETIME,
          correction_method VARCHAR(255),
          attempts INT DEFAULT 1,
          last_error_time DATETIME,
          INDEX idx_user_id (user_id),
          INDEX idx_user_course (user_id, course_id),
          INDEX idx_word_course (word, course_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci`;

      const createWordRewardsCourseTable = `
        CREATE TABLE IF NOT EXISTS word_rewards_course (
          user_id INT NOT NULL,
          course_id VARCHAR(100) NOT NULL,
          data JSON,
          last_modified DATETIME,
          PRIMARY KEY(user_id, course_id),
          INDEX idx_user_id (user_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci`;

      const createErrorWordsCourseTable = `
        CREATE TABLE IF NOT EXISTS error_words_course (
          user_id INT NOT NULL,
          course_id VARCHAR(100) NOT NULL,
          data JSON,
          last_modified DATETIME,
          PRIMARY KEY(user_id, course_id),
          INDEX idx_user_id (user_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci`;

      const createPracticeHistoryTable = `
        CREATE TABLE IF NOT EXISTS practice_history (
          id INT PRIMARY KEY AUTO_INCREMENT,
          user_id INT NOT NULL,
          course_id VARCHAR(100) NOT NULL,
          type ENUM('normal_practice', 'error_correction', 'error_challenge', 'random_challenge') NOT NULL,
          timestamp DATETIME NOT NULL,
          accuracy DECIMAL(5,2) NOT NULL,
          correct_count INT NOT NULL DEFAULT 0,
          total_count INT NOT NULL DEFAULT 0,
          practice_time INT DEFAULT 0,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          
          INDEX idx_user_id (user_id),
          INDEX idx_course_id (course_id),
          INDEX idx_type (type),
          INDEX idx_timestamp (timestamp),
          INDEX idx_user_course (user_id, course_id),
          INDEX idx_user_type (user_id, type)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci`;

      const createPaymentOrdersTable = `
        CREATE TABLE IF NOT EXISTS payment_orders (
          id INT PRIMARY KEY AUTO_INCREMENT,
          order_id VARCHAR(100) NOT NULL UNIQUE,
          user_id INT NOT NULL,
          openid VARCHAR(255) NOT NULL,
          
          vip_type ENUM('monthly', 'quarterly', 'yearly', 'lifetime') NOT NULL,
          amount DECIMAL(10,2) NOT NULL,
          
          status ENUM('pending', 'completed', 'failed', 'cancelled') DEFAULT 'pending',
          
          prepay_id VARCHAR(100),
          transaction_id VARCHAR(100),
          
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          completed_at DATETIME,
          
          INDEX idx_order_id (order_id),
          INDEX idx_user_id (user_id),
          INDEX idx_openid (openid),
          INDEX idx_status (status),
          INDEX idx_created_at (created_at),
          
          FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci`;

      const createVipStatusTable = `
        CREATE TABLE IF NOT EXISTS user_vip_status (
          id INT PRIMARY KEY AUTO_INCREMENT,
          user_id INT NOT NULL,
          openid VARCHAR(255) NOT NULL,
          
          is_vip BOOLEAN DEFAULT FALSE,
          vip_type ENUM('monthly', 'quarterly', 'yearly', 'lifetime', 'custom') DEFAULT NULL,
          
          vip_start_time DATETIME DEFAULT NULL,
          vip_end_time DATETIME DEFAULT NULL,
          
          purchase_source ENUM('wechat_pay', 'gift', 'promotion', 'admin') DEFAULT 'admin',
          purchase_order_id VARCHAR(100),
          purchase_amount DECIMAL(10,2) DEFAULT 0,
          
          status ENUM('active', 'expired', 'cancelled') DEFAULT 'active',
          auto_renew BOOLEAN DEFAULT FALSE,
          
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          
          INDEX idx_user_id (user_id),
          INDEX idx_openid (openid),
          INDEX idx_vip_end_time (vip_end_time),
          INDEX idx_status (status),
          
          FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci`;

      const createVipPrivilegesTable = `
        CREATE TABLE IF NOT EXISTS vip_privileges (
          id INT PRIMARY KEY AUTO_INCREMENT,
          privilege_key VARCHAR(50) NOT NULL UNIQUE,
          privilege_name VARCHAR(100) NOT NULL,
          description TEXT,
          
          privilege_type ENUM('feature', 'limit', 'resource') NOT NULL,
          
          free_user_limit INT DEFAULT 0,
          vip_user_limit INT DEFAULT -1,
          
          is_active BOOLEAN DEFAULT TRUE,
          sort_order INT DEFAULT 0,
          
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci`;
      
      console.log("【诊断日志】即将执行的 users 表创建语句 (最终版)：");
      console.log(createUsersTable);
      
      await connection.execute(createUsersTable);
      // 不再删除 user_data_sync 表，改为按需创建/升级
      await connection.execute(createUserDataSyncTable);
      // ❌ 已弃用 error_words / word_rewards 主表，跳过创建
      // await connection.execute(createWordRewardsTable);
      // await connection.execute(createErrorWordsTable);
      await connection.execute(createWordRewardsCourseTable);
      await connection.execute(createErrorWordsCourseTable);
      await connection.execute(createPracticeHistoryTable);
      await connection.execute(createPaymentOrdersTable);
      await connection.execute(createVipStatusTable);
      // await connection.execute(createVipPrivilegesTable); // 已弃用 vip_privileges 表，不再创建

      
      
      // ❌ 已弃用 error_words 主表，跳过字段迁移与索引优化
    } catch (error) {
      console.error('❌ MySQL 表结构初始化失败:', error);
      throw error;
    } finally {
      connection.release();
    }
  }

  

  static async close() {
    if (this.pool) {
      await this.pool.end();
      this.pool = null;
      console.log(' MySQL 连接池已关闭');
    }
  }

  static async saveUserDataSync(userId, syncData) {
    const connection = await this.pool.getConnection();
    try {
      await connection.beginTransaction();
      const syncStartTime = Date.now();
      
      const { textbookWordRewards, errorWords, ...mainSyncData } = syncData;

      // 拆分主数据字段（已与云函数统一）
      const {
        openid = null,
        userData = {},
        textbookData = {},
        currentTextbook = {},
        learningProgress = {},
        dailyTasksData = {},
        lastModified = new Date().toISOString()
      } = mainSyncData;

      let openidValue = openid;
      if (!openidValue) {
        // 若未传递 openid，则从 users 表查询
        const [uRows] = await connection.execute('SELECT openid FROM users WHERE id = ?', [userId]);
        if (uRows.length > 0) {
          openidValue = uRows[0].openid;
        }
      }

      const now = this.formatISODateTime(new Date());
      const lastMod = this.formatISODateTime(lastModified);

      // =========== 保护逻辑：避免把非空云端数据覆盖为空 ===========
      let sanitizedUserData = userData;
      if (!sanitizedUserData || Object.keys(sanitizedUserData).length === 0) {
        // 尝试读取现有记录，若已有非空 user_data 则保留
        const [existingRows] = await connection.execute(
          'SELECT user_data FROM user_data_sync WHERE user_id = ? LIMIT 1',
          [userId]
        );
        if (existingRows.length > 0 && existingRows[0].user_data) {
          try {
            const parsed = typeof existingRows[0].user_data === 'string'
              ? JSON.parse(existingRows[0].user_data)
              : existingRows[0].user_data;
            if (parsed && Object.keys(parsed).length > 0) {
              sanitizedUserData = parsed;
              console.log('[DatabaseService] 检测到上传 userData 为空，已使用云端现有数据保持完整性');
            }
          } catch (parseErr) {
            console.warn('[DatabaseService] 解析现有 user_data 失败，继续使用上传值(空)');
          }
        }
      }

      // =========== 如果上传的数据整体为空，直接跳过更新 ==========
      const hasEffectiveData = (
        (sanitizedUserData && Object.keys(sanitizedUserData).length > 0) ||
        Object.keys(textbookData || {}).length > 0 ||
        Object.keys(currentTextbook || {}).length > 0 ||
        Object.keys(learningProgress || {}).length > 0 ||
        Object.keys(dailyTasksData || {}).length > 0 ||
        (textbookWordRewards && Object.keys(textbookWordRewards).length > 0) ||
        (errorWords && errorWords.length > 0)
      );

      if (!hasEffectiveData) {
        console.warn('[DatabaseService] 收到空数据包，已跳过更新并保持云端数据不变');
        await connection.rollback();
        return { success: true, skipped: true, message: '空数据包，跳过更新' };
      }

      // 插入或更新 user_data_sync 主表
      await connection.execute(
        `INSERT INTO user_data_sync (user_id, openid, user_data, textbook_data, current_textbook, learning_progress, daily_tasks_data, last_modified, update_time)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
         ON DUPLICATE KEY UPDATE
           openid = VALUES(openid),
           user_data = VALUES(user_data),
           textbook_data = VALUES(textbook_data),
           current_textbook = VALUES(current_textbook),
           learning_progress = VALUES(learning_progress),
           daily_tasks_data = VALUES(daily_tasks_data),
           last_modified = VALUES(last_modified),
           update_time = VALUES(update_time)`,
        [
          userId,
          openidValue,
          JSON.stringify(sanitizedUserData),
          JSON.stringify(textbookData),
          JSON.stringify(currentTextbook),
          JSON.stringify(learningProgress),
          JSON.stringify(dailyTasksData),
          lastMod,
          now
        ]
      );

      // 处理 word_rewards (course-level aggregation)
      if (textbookWordRewards && typeof textbookWordRewards === 'object') {
        // 直接清空该用户在新表的数据，再批量插入
        await connection.execute('DELETE FROM word_rewards_course WHERE user_id = ?', [userId]);

        const insertPackSql = `INSERT INTO word_rewards_course (user_id, course_id, data, last_modified) VALUES (?, ?, ?, ?) ON DUPLICATE KEY UPDATE data = VALUES(data), last_modified = VALUES(last_modified)`;

        for (const [key, rewards] of Object.entries(textbookWordRewards)) {
          const keyParts = key.split('_');
          if (keyParts.length !== 2 || keyParts[0] !== 'wordRewards') continue;
          const courseId = keyParts[1];
          await connection.execute(insertPackSql, [
            userId,
            courseId,
            JSON.stringify(rewards),
            now
          ]);
        }
      }

      // 处理 error_words (course-level aggregation)
      if (errorWords && Array.isArray(errorWords)) {
        await connection.execute('DELETE FROM error_words_course WHERE user_id = ?', [userId]);
        const insertErrPackSql = `INSERT INTO error_words_course (user_id, course_id, data, last_modified) VALUES (?, ?, ?, ?) ON DUPLICATE KEY UPDATE data = VALUES(data), last_modified = VALUES(last_modified)`;

        // 按 courseId 分组
        const grouped = {};
        for (const err of errorWords) {
          const courseId = err.courseInfo?.courseId || 'unknown';
          if (!grouped[courseId]) grouped[courseId] = [];
          grouped[courseId].push(err);
        }
        for (const [courseId, errs] of Object.entries(grouped)) {
          await connection.execute(insertErrPackSql, [
            userId,
            courseId,
            JSON.stringify(errs),
            now
          ]);
        }
      }

      // 处理 practice_history 数据
      if (syncData.practiceHistory && Array.isArray(syncData.practiceHistory)) {
        const insertHistorySql = `
          INSERT INTO practice_history 
          (user_id, course_id, type, timestamp, accuracy, correct_count, total_count, practice_time) 
          VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        `;
        
        for (const record of syncData.practiceHistory) {
          await connection.execute(insertHistorySql, [
            userId,
            record.course_id,
            record.type,
            this.formatISODateTime(record.timestamp), // 修复：使用 formatISODateTime 转换时间戳格式
            record.accuracy,
            record.correctCount,
            record.totalCount,
            record.practiceTime || 0
          ]);
        }
      }

      await connection.commit();
      
      const syncEndTime = Date.now();
      console.log(`[Performance] User ${userId} total sync time: ${syncEndTime - syncStartTime}ms`);
      
      return { success: true, message: 'Data saved successfully' };
    } catch (error) {
      await connection.rollback();
      console.error('保存用户同步数据失败:', error);
      return { success: false, error: error.message };
    } finally {
      connection.release();
    }
  }

  static async getUserDataToSync(userId) {
    if (this.testMode) {
      console.log('🧪 测试模式：获取用户同步数据，userId:', userId);
      
      // 为不同用户返回不同的模拟数据，确保与前端计算逻辑一致
      const mockDataByUserId = {
        1: {
          userData: {
            totalPracticeCount: 15,
            totalCorrectCount: 12,
            totalWrongCount: 3,
            accuracyRate: 80,
            totalPracticeTime: 1800
          },
          textbookData: {
            totalTime: 1800, // 累计时长（秒），与前端个人中心一致
            accuracyRate: 80
          },
          currentTextbook: {
            publisher: { id: 'renjiao', name: '人教版' },
            grade: { id: 'grade1', name: '一年级' },
            term: 'term1'
          },
          learningProgress: {
            'renjiao_grade1_term1_unit1': {
              practiceCount: 5,
              totalTime: 600,
              completed: true
            },
            'renjiao_grade1_term1_unit2': {
              practiceCount: 10,
              totalTime: 1200,
              completed: false
            }
          },
          errorWords: [
            {
              word: '测试错题1',
              corrected: false,
              courseInfo: {
                publisherId: 'renjiao',
                gradeId: 'grade1',
                term: 'term1'
              }
            },
            {
              word: '测试错题2',
              corrected: true,
              courseInfo: {
                publisherId: 'renjiao',
                gradeId: 'grade1',
                term: 'term1'
              }
            }
          ],
          textbookWordRewards: {},
          lastModified: new Date().toISOString(),
          syncTime: new Date().toISOString()
        },
        2: {
          userData: {
            totalPracticeCount: 6,
            totalCorrectCount: 4,
            totalWrongCount: 2,
            accuracyRate: 70,
            totalPracticeTime: 900
          },
          textbookData: {
            totalTime: 900, // 累计时长（秒），与前端个人中心一致
            accuracyRate: 70
          },
          currentTextbook: {
            publisher: { id: 'renjiaoban', name: '人教版' },
            grade: { id: 'grade1', name: '一年级' },
            term: 'term2'
          },
          learningProgress: {
            'renjiaoban_grade1_term2_30340': {
              practiceCount: 1,
              totalTime: 450,
              completed: true
            },
            'renjiaoban_grade1_term2_30347': {
              practiceCount: 1,
              totalTime: 450,
              completed: false
            }
          },
          errorWords: [
            {
              word: '下册错题1',
              corrected: false,
              courseInfo: {
                publisherId: 'renjiaoban',
                gradeId: 'grade1',
                term: 'term2'
              }
            },
            {
              word: '下册错题2',
              corrected: true,
              courseInfo: {
                publisherId: 'renjiaoban',
                gradeId: 'grade1',
                term: 'term2'
              }
            }
          ],
          textbookWordRewards: {},
          lastModified: new Date().toISOString(),
          syncTime: new Date().toISOString()
        },
        3: {
          userData: {
            totalPracticeCount: 6,
            totalCorrectCount: 4,
            totalWrongCount: 2,
            accuracyRate: 70,
            totalPracticeTime: 900
          },
          textbookData: {
            totalTime: 900, // 累计时长（秒），与前端个人中心一致
            accuracyRate: 70
          },
          currentTextbook: {
            publisher: { id: 'renjiao', name: '人教版' },
            grade: { id: 'grade1', name: '一年级' },
            term: 'term2'
          },
          learningProgress: {
            'renjiao_grade1_term2_unit1': {
              practiceCount: 3,
              totalTime: 450,
              completed: true
            },
            'renjiao_grade1_term2_unit2': {
              practiceCount: 3,
              totalTime: 450,
              completed: false
            }
          },
          errorWords: [
            {
              word: '下册错题1',
              corrected: false,
              courseInfo: {
                publisherId: 'renjiao',
                gradeId: 'grade1',
                term: 'term2'
              }
            },
            {
              word: '下册错题2',
              corrected: true,
              courseInfo: {
                publisherId: 'renjiao',
                gradeId: 'grade1',
                term: 'term2'
              }
            }
          ],
          textbookWordRewards: {},
          lastModified: new Date().toISOString(),
          syncTime: new Date().toISOString()
        }
      };
      
      // **修复：确保获取指定用户的模拟数据，如果没有则使用默认数据**
      const mockData = mockDataByUserId[userId] || mockDataByUserId[1];
      
      return { success: true, data: mockData };
    }

    const connection = await this.pool.getConnection();
    try {
      // 1. 获取主同步数据
      const [mainRows] = await connection.execute('SELECT user_data, textbook_data, current_textbook, learning_progress, daily_tasks_data, last_modified FROM user_data_sync WHERE user_id = ?', [userId]);
      let syncData = {};
      let lastModified = null;
      if (mainRows.length > 0) {
        const row = mainRows[0];
        syncData = {
          userData: row.user_data ? (typeof row.user_data === 'string' ? JSON.parse(row.user_data) : row.user_data) : {},
          textbookData: row.textbook_data ? (typeof row.textbook_data === 'string' ? JSON.parse(row.textbook_data) : row.textbook_data) : {},
          currentTextbook: row.current_textbook ? (typeof row.current_textbook === 'string' ? JSON.parse(row.current_textbook) : row.current_textbook) : {},
          learningProgress: row.learning_progress ? (typeof row.learning_progress === 'string' ? JSON.parse(row.learning_progress) : row.learning_progress) : {},
          dailyTasksData: row.daily_tasks_data ? (typeof row.daily_tasks_data === 'string' ? JSON.parse(row.daily_tasks_data) : row.daily_tasks_data) : {}
        };
        lastModified = row.last_modified;
      }

      // 2. 获取 word_rewards 数据 (course-level)
      const [rewardPackRows] = await connection.execute('SELECT course_id, data FROM word_rewards_course WHERE user_id = ?', [userId]);
      const textbookWordRewards = {};
      rewardPackRows.forEach(row => {
        const rewardsObj = typeof row.data === 'string' ? JSON.parse(row.data) : row.data;
        const storageKey = `wordRewards_${row.course_id}`;
        textbookWordRewards[storageKey] = rewardsObj;
      });

      // 3. 获取 error_words 数据 (course-level)
      const [errorPackRows] = await connection.execute('SELECT course_id, data FROM error_words_course WHERE user_id = ?', [userId]);
      let errorWords = [];
      errorPackRows.forEach(row => {
        const errs = typeof row.data === 'string' ? JSON.parse(row.data) : row.data;
        if (Array.isArray(errs)) errorWords.push(...errs);
      });

      // 4. 判断是否存在任何数据
      const hasMainData = mainRows.length > 0;
      const hasRewards = Object.keys(textbookWordRewards).length > 0;
      const hasErrorWords = errorWords.length > 0;

      if (!hasMainData && !hasRewards && !hasErrorWords) {
        // 只有当所有数据表都为空时，才返回 null
        return { success: true, data: null };
      }

      // 5. 组合最终数据
      const completeData = {
        ...syncData,
        textbookWordRewards,
        errorWords,
        // 如果主数据存在，使用其时间戳；否则，使用当前时间作为最后修改时间
        lastModified: lastModified ? new Date(lastModified).toISOString() : new Date().toISOString(),
        syncTime: lastModified ? new Date(lastModified).toISOString() : new Date().toISOString()
      };
      
      return { success: true, data: completeData };
    } catch (error) {
      console.error('获取用户同步数据失败:', error);
      return { success: false, error: error.message };
    } finally {
      connection.release();
    }
  }
}

module.exports = DatabaseService;
