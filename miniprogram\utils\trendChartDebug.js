/**
 * 趋势图调试工具
 * 用于调试趋势图数据获取和API调用问题
 */

const TrendChartData = require('./trendChartData');
const cloudApi = require('./cloudApi');
const SimpleStorage = require('./simpleStorage');

class TrendChartDebug {
  
  /**
   * 调试API连接
   */
  static async debugApiConnection() {
    console.log('=== API连接调试 ===');
    
    try {
      // 测试API配置
      console.log('API配置:', cloudApi.config);
      
      // 测试练习历史API
      console.log('正在调用练习历史API...');
      const response = await cloudApi.user.getPracticeHistory({
        limit: 10,
        showLoading: false
      });
      
      console.log('API响应:', response);
      
      if (response.success) {
        console.log('✅ API调用成功');
        console.log('返回数据数量:', response.data ? response.data.length : 0);
        if (response.data && response.data.length > 0) {
          console.log('第一条数据示例:', response.data[0]);
        }
        return true;
      } else {
        console.log('❌ API调用失败:', response.error);
        return false;
      }
      
    } catch (error) {
      console.error('💥 API调用异常:', error);
      return false;
    }
  }
  
  /**
   * 调试本地数据
   */
  static debugLocalData() {
    console.log('=== 本地数据调试 ===');
    
    try {
      // 检查本地存储
      const practiceHistory = SimpleStorage.getPracticeHistory();
      const practiceRecords = SimpleStorage.getPracticeRecords();
      
      console.log('本地数据源:');
      console.log('- practiceHistory:', practiceHistory.length, '条');
      console.log('- practiceRecords:', practiceRecords.length, '条');
      
      if (practiceHistory.length > 0) {
        console.log('practiceHistory示例:', practiceHistory.slice(0, 3));
      }
      
      if (practiceRecords.length > 0) {
        console.log('practiceRecords示例:', practiceRecords.slice(0, 3));
      }
      
      // 检查微信存储
      const wxPracticeHistory = wx.getStorageSync('practiceHistory') || [];
      const wxPracticeRecords = wx.getStorageSync('practiceRecords') || [];
      
      console.log('微信存储:');
      console.log('- practiceHistory:', wxPracticeHistory.length, '条');
      console.log('- practiceRecords:', wxPracticeRecords.length, '条');
      
      return {
        practiceHistory: practiceHistory.length,
        practiceRecords: practiceRecords.length,
        wxPracticeHistory: wxPracticeHistory.length,
        wxPracticeRecords: wxPracticeRecords.length
      };
      
    } catch (error) {
      console.error('💥 本地数据检查异常:', error);
      return null;
    }
  }
  
  /**
   * 调试趋势图数据生成
   */
  static async debugTrendDataGeneration() {
    console.log('=== 趋势图数据生成调试 ===');
    
    try {
      console.log('正在获取趋势图数据...');
      const trendData = await TrendChartData.getTrendData(7);
      
      console.log('趋势图数据:', trendData);
      console.log('数据摘要:', trendData.summary);
      console.log('标签数量:', trendData.labels.length);
      console.log('练习次数数据:', trendData.practiceCount);
      console.log('正确率数据:', trendData.accuracy);
      console.log('练习时间数据:', trendData.totalTime);
      
      // 验证数据完整性
      const isValid = this.validateTrendData(trendData);
      console.log('数据验证结果:', isValid ? '✅ 通过' : '❌ 失败');
      
      return trendData;
      
    } catch (error) {
      console.error('💥 趋势图数据生成异常:', error);
      return null;
    }
  }
  
  /**
   * 验证趋势图数据
   */
  static validateTrendData(data) {
    const requiredFields = ['labels', 'practiceCount', 'accuracy', 'totalTime', 'maxValues', 'summary'];
    
    for (const field of requiredFields) {
      if (!data.hasOwnProperty(field)) {
        console.error('缺少必要字段:', field);
        return false;
      }
    }
    
    // 检查数组长度一致性
    const arrayFields = ['labels', 'practiceCount', 'accuracy', 'totalTime'];
    const expectedLength = data.labels.length;
    
    for (const field of arrayFields) {
      if (data[field].length !== expectedLength) {
        console.error(`字段 ${field} 长度不一致:`, data[field].length, '期望:', expectedLength);
        return false;
      }
    }
    
    return true;
  }
  
  /**
   * 生成测试数据并验证
   */
  static async generateTestDataAndVerify() {
    console.log('=== 生成测试数据并验证 ===');
    
    try {
      // 生成测试数据
      const testData = this.generateTestPracticeHistory();
      console.log('生成测试数据:', testData.length, '条');
      
      // 存储到本地
      SimpleStorage.setPracticeHistory(testData);
      console.log('测试数据已存储到本地');
      
      // 验证趋势图生成
      const trendData = await TrendChartData.getTrendData(7);
      console.log('基于测试数据的趋势图:', trendData.summary);
      
      return trendData.summary.totalPractices > 0;
      
    } catch (error) {
      console.error('💥 测试数据生成验证异常:', error);
      return false;
    }
  }
  
  /**
   * 生成测试练习历史数据
   */
  static generateTestPracticeHistory() {
    const testData = [];
    const today = new Date();
    
    // 生成过去7天的数据
    for (let i = 6; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(today.getDate() - i);
      
      // 每天1-3次练习
      const practiceCount = Math.floor(Math.random() * 3) + 1;
      
      for (let j = 0; j < practiceCount; j++) {
        const record = {
          timestamp: new Date(date.getTime() + j * 3600000).toISOString(),
          accuracy: Math.floor(Math.random() * 40) + 60, // 60-100%
          correctCount: Math.floor(Math.random() * 8) + 2, // 2-10个
          totalCount: 10,
          practiceTime: Math.floor(Math.random() * 300) + 180, // 3-8分钟
          type: 'normal_practice',
          courseId: 'test_course'
        };
        
        testData.push(record);
      }
    }
    
    return testData;
  }
  
  /**
   * 完整调试流程
   */
  static async runFullDebug() {
    console.log('🚀 开始完整调试流程...');
    
    const results = {
      apiConnection: false,
      localData: null,
      trendDataGeneration: null,
      testDataVerification: false
    };
    
    // 1. 调试API连接
    console.log('\n1. 调试API连接');
    results.apiConnection = await this.debugApiConnection();
    
    // 2. 调试本地数据
    console.log('\n2. 调试本地数据');
    results.localData = this.debugLocalData();
    
    // 3. 调试趋势图数据生成
    console.log('\n3. 调试趋势图数据生成');
    results.trendDataGeneration = await this.debugTrendDataGeneration();
    
    // 4. 生成测试数据并验证
    console.log('\n4. 生成测试数据并验证');
    results.testDataVerification = await this.generateTestDataAndVerify();
    
    // 输出调试结果
    console.log('\n📋 调试结果汇总:');
    console.log('- API连接:', results.apiConnection ? '✅ 正常' : '❌ 异常');
    console.log('- 本地数据:', results.localData ? '✅ 有数据' : '❌ 无数据');
    console.log('- 趋势图生成:', results.trendDataGeneration ? '✅ 成功' : '❌ 失败');
    console.log('- 测试数据验证:', results.testDataVerification ? '✅ 通过' : '❌ 失败');
    
    // 给出建议
    this.provideSuggestions(results);
    
    return results;
  }
  
  /**
   * 根据调试结果提供建议
   */
  static provideSuggestions(results) {
    console.log('\n💡 建议:');
    
    if (!results.apiConnection) {
      console.log('- 检查网络连接和API配置');
      console.log('- 确认服务器是否正常运行');
      console.log('- 检查用户认证状态');
    }
    
    if (!results.localData || (results.localData.practiceHistory === 0 && results.localData.practiceRecords === 0)) {
      console.log('- 本地无练习数据，建议先进行一些练习');
      console.log('- 或者使用测试数据进行调试');
    }
    
    if (!results.trendDataGeneration) {
      console.log('- 检查数据处理逻辑');
      console.log('- 确认数据格式是否正确');
    }
    
    if (results.testDataVerification) {
      console.log('- 测试数据验证通过，功能正常');
      console.log('- 可以清理测试数据，使用真实数据');
    }
  }
  
  /**
   * 清理测试数据
   */
  static cleanupTestData() {
    try {
      wx.removeStorageSync('practiceHistory');
      console.log('🧹 测试数据已清理');
    } catch (error) {
      console.error('清理测试数据失败:', error);
    }
  }
}

module.exports = TrendChartDebug;
