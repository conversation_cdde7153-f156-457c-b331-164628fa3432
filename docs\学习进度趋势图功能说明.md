# 学习进度趋势图功能说明

## 功能概述

学习进度趋势图是为学习进度页面新增的数据可视化功能，通过Canvas 2D技术绘制用户训练历史数据的变化趋势，帮助用户和家长更直观地了解学习进展。

## 主要特性

### 1. 多维度数据展示
- **练习次数**: 显示每日练习次数变化
- **正确率**: 展示学习准确率趋势
- **练习时间**: 记录每日学习时长分布

### 2. 交互式图表
- **触摸查看**: 点击图表可查看具体数值
- **数据切换**: 支持在不同数据类型间切换
- **实时提示**: 显示具体日期和数值信息

### 3. 智能数据处理
- **多源数据**: 自动整合不同格式的练习记录
- **空数据处理**: 优雅处理无数据情况
- **时间范围**: 默认显示最近7天数据

## 技术实现

### 核心模块

#### 1. TrendChartData (数据处理模块)
```javascript
// 获取趋势图数据
const trendData = TrendChartData.getTrendData(7, targetUserId);

// 数据结构
{
  labels: ['1/1', '1/2', ...],        // 日期标签
  practiceCount: [3, 5, 2, ...],     // 练习次数
  accuracy: [85, 90, 78, ...],       // 正确率
  totalTime: [45, 60, 30, ...],      // 练习时间(分钟)
  maxValues: { ... },                // 最大值
  summary: { ... }                   // 统计摘要
}
```

#### 2. TrendChart (绘制模块)
```javascript
// 创建图表实例
const chart = new TrendChart(canvas, ctx, options);

// 绘制图表
chart.drawChart(data, 'practiceCount');

// 处理触摸交互
const touchInfo = chart.handleTouch(x, y);
```

### 数据来源

#### 本地数据
- `practiceHistory`: 标准化练习历史记录
- `practiceRecords`: 原始练习记录
- 自动转换和合并不同格式的数据

#### 远程数据
- 支持查看他人学习数据（需要授权）
- 通过API获取目标用户的练习历史

## 使用方法

### 1. 页面集成
趋势图已集成到学习进度页面 (`/pages/learning-progress/learning-progress`)

### 2. 数据类型切换
用户可以通过页面顶部的选项卡切换查看：
- 练习次数
- 正确率
- 练习时间

### 3. 交互操作
- **点击图表**: 查看具体数值
- **滑动图表**: 查看不同时间点数据
- **松开手指**: 隐藏提示信息

## 样式定制

### CSS类名
```css
.trend-chart              /* 趋势图容器 */
.chart-container          /* 图表区域 */
.trend-canvas             /* Canvas画布 */
.data-type-selector       /* 数据类型选择器 */
.chart-tooltip            /* 触摸提示框 */
.chart-summary            /* 统计摘要 */
```

### 颜色配置
```javascript
colors: {
  practiceCount: '#667eea',  // 练习次数
  accuracy: '#4CAF50',       // 正确率
  totalTime: '#FF9800',      // 练习时间
  grid: '#E0E0E0',          // 网格线
  text: '#666666'           // 文字
}
```

## 测试验证

### 运行测试
```javascript
// 在控制台中运行
const TrendChartTest = require('./utils/trendChartTest');
TrendChartTest.runAllTests();
```

### 测试内容
1. **数据处理功能**: 验证数据提取和聚合
2. **空数据处理**: 测试无数据情况
3. **格式转换**: 验证不同数据格式的兼容性

## 性能优化

### Canvas优化
- 使用Canvas 2D API提升渲染性能
- 设备像素比适配确保清晰度
- 触摸事件节流避免过度重绘

### 数据优化
- 本地数据缓存减少计算开销
- 智能去重避免重复数据
- 按需加载减少内存占用

## 故障排除

### 常见问题

#### 1. 图表不显示
- 检查Canvas初始化是否成功
- 确认数据是否正确加载
- 验证页面渲染完成后再初始化

#### 2. 触摸无响应
- 确认Canvas尺寸设置正确
- 检查触摸事件绑定
- 验证坐标转换计算

#### 3. 数据不准确
- 检查本地存储数据格式
- 确认数据聚合逻辑
- 验证时间范围计算

### 调试方法
```javascript
// 开启详细日志
console.log('[TrendChart] 调试信息');

// 检查数据结构
console.log('趋势图数据:', trendData);

// 验证Canvas状态
console.log('Canvas尺寸:', canvas.width, canvas.height);
```

## 扩展功能

### 未来优化方向
1. **更多图表类型**: 柱状图、饼图等
2. **时间范围选择**: 支持周、月、年视图
3. **数据导出**: 支持图表和数据导出
4. **动画效果**: 添加绘制动画
5. **主题定制**: 支持多种视觉主题

### 集成建议
- 可以将趋势图组件提取为独立组件
- 支持在其他页面中复用
- 考虑集成专业图表库(如ECharts)

## 相关文件

### 核心文件
- `miniprogram/utils/trendChartData.js` - 数据处理模块
- `miniprogram/utils/trendChart.js` - 图表绘制模块
- `miniprogram/pages/learning-progress/learning-progress.js` - 页面逻辑
- `miniprogram/pages/learning-progress/learning-progress.wxml` - 页面结构
- `miniprogram/pages/learning-progress/learning-progress.wxss` - 页面样式

### 测试文件
- `miniprogram/utils/trendChartTest.js` - 功能测试
- `docs/学习进度趋势图功能说明.md` - 使用说明

---

**开发完成时间**: 2025年1月
**技术栈**: 微信小程序 + Canvas 2D + JavaScript
**兼容性**: 支持微信小程序基础库 2.9.0+
