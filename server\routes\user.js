const express = require('express');
const router = express.Router();
const UserService = require('../services/userService');
const DatabaseService = require('../services/databaseService'); // Added for practice history

/**
 * 用户管理路由
 * 对应云函数: userManager
 */

// 用户注册
router.post('/register', async (req, res) => {
  try {
    console.log('[User API] 用户注册请求:', {
      hasData: !!req.body.data,
      nickname: req.body.data?.nickname
    });
    
    const { data: userData } = req.body;
    
    if (!userData) {
      return res.json({
        success: false,
        error: '缺少用户数据参数'
      });
    }

    const result = await UserService.registerUser(userData);
    
    console.log('[User API] 用户注册结果:', {
      success: result.success,
      userId: result.user?._id
    });
    
    res.json(result);
    
  } catch (error) {
    console.error('[User API] 用户注册失败:', error);
    res.json({
      success: false,
      error: error.message || '用户注册失败'
    });
  }
});

// 用户登录
router.post('/login', async (req, res) => {
  try {
    console.log('[User API] 用户登录请求');
    
    // 从请求头或 body 获取用户标识
    const userOpenid = req.headers['x-openid'] || req.body.userOpenid;
    
    if (!userOpenid) {
      return res.json({
        success: false,
        needsRegistration: true,
        error: '缺少用户标识信息'
      });
    }

    const result = await UserService.loginUser(userOpenid);
    
    console.log('[User API] 用户登录结果:', {
      success: result.success,
      needsRegistration: result.needsRegistration,
      userId: result.user?._id
    });
    
    res.json(result);
    
  } catch (error) {
    console.error('[User API] 用户登录失败:', error);
    res.json({
      success: false,
      error: error.message || '用户登录失败'
    });
  }
});

// 微信登录
router.post('/wx-login', async (req, res) => {
  try {
    const { code } = req.body;
    console.log('[User API] 微信登录请求:', { hasCode: !!code });

    const result = await UserService.loginWithWxCode(code);

    console.log('[User API] 微信登录结果:', {
      success: result.success,
      userId: result.user?._id
    });
    
    res.json(result);

  } catch (error) {
    console.error('[User API] 微信登录失败:', error);
    res.json({
      success: false,
      error: error.message || '微信登录失败'
    });
  }
});

// 更新用户资料
router.post('/update-profile', async (req, res) => {
  try {
    console.log('[User API] 更新用户资料请求');
    
    const { userId, profileData } = req.body;
    
    if (!userId || !profileData) {
      return res.json({
        success: false,
        error: '缺少必要参数'
      });
    }

    const result = await UserService.updateUserProfile(userId, profileData);
    
    console.log('[User API] 更新用户资料结果:', result);
    
    res.json(result);
    
  } catch (error) {
    console.error('[User API] 更新用户资料失败:', error);
    res.json({
      success: false,
      error: error.message || '更新用户资料失败'
    });
  }
});

// 获取用户信息
router.get('/profile/:userId', async (req, res) => {
  try {
    const { userId } = req.params;
    
    console.log('[User API] 获取用户信息请求:', userId);
    
    if (!userId) {
      return res.json({
        success: false,
        error: '缺少用户ID参数'
      });
    }

    const result = await UserService.getUserProfile(userId);
    
    console.log('[User API] 获取用户信息结果:', {
      success: result.success,
      hasUser: !!result.user
    });
    
    res.json(result);
    
  } catch (error) {
    console.error('[User API] 获取用户信息失败:', error);
    res.json({
      success: false,
      error: error.message || '获取用户信息失败'
    });
  }
});

// 绑定用户 - 授权对方查看我的数据
router.post('/bind', async (req, res) => {
  try {
    console.log('[User API] 绑定用户请求:', req.body);
    
    const { targetId } = req.body;
    const currentOpenid = req.headers['x-openid']; // 从请求头获取当前用户openid
    
    if (!targetId || !currentOpenid) {
      return res.json({
        success: false,
        code: 400,
        error: '缺少必要参数'
      });
    }

    const result = await UserService.bindUser(currentOpenid, targetId);
    res.json(result);
    
  } catch (error) {
    console.error('[User API] 绑定用户失败:', error);
    res.json({
      success: false,
      code: 500,
      error: error.message || '绑定用户失败'
    });
  }
});

// 解绑用户
router.post('/unbind', async (req, res) => {
  try {
    console.log('[User API] 解绑用户请求:', req.body);
    
    const { watcherId } = req.body;
    const currentOpenid = req.headers['x-openid'];
    
    if (!watcherId || !currentOpenid) {
      return res.json({
        success: false,
        code: 400,
        error: '缺少必要参数'
      });
    }

    const result = await UserService.unbindUser(currentOpenid, watcherId);
    res.json(result);
    
  } catch (error) {
    console.error('[User API] 解绑用户失败:', error);
    res.json({
      success: false,
      code: 500,
      error: error.message || '解绑用户失败'
    });
  }
});

// 获取绑定列表
router.get('/bind/list', async (req, res) => {
  try {
    const currentOpenid = req.headers['x-openid'];
    
    console.log('[User API] 获取绑定列表请求:', {
      hasXOpenid: !!req.headers['x-openid'],
      headers: Object.keys(req.headers)
    });
    
    if (!currentOpenid) {
      return res.json({
        success: false,
        code: 400,
        error: '缺少用户标识'
      });
    }

    const result = await UserService.getBindList(currentOpenid);
    res.json(result);
    
  } catch (error) {
    console.error('[User API] 获取绑定列表失败:', error);
    res.json({
      success: false,
      code: 500,
      error: error.message || '获取绑定列表失败'
    });
  }
});

// 获取学习进度数据
router.get('/progress', async (req, res) => {
  try {
    const { targetId } = req.query;
    const currentOpenid = req.headers['x-openid'];
    
    if (!currentOpenid) {
      return res.json({
        success: false,
        code: 400,
        error: '缺少用户标识'
      });
    }

    // 如果没有指定targetId，则查看自己的数据
    const finalTargetId = targetId || currentOpenid;
    
    const result = await UserService.getProgressData(currentOpenid, finalTargetId);
    res.json(result);
    
  } catch (error) {
    console.error('[User API] 获取学习进度失败:', error);
    res.json({
      success: false,
      code: 500,
      error: error.message || '获取学习进度失败'
    });
  }
});

// 获取练习历史记录
router.get('/practice-history', async (req, res) => {
  try {
    const currentOpenid = req.headers['x-openid'];
    const { courseId, type, startDate, endDate, limit, offset } = req.query;
    
    if (!currentOpenid) {
      return res.json({
        success: false,
        code: 400,
        error: '缺少用户标识'
      });
    }
    
    const user = await DatabaseService.getUserByOpenid(currentOpenid);
    if (!user) {
      return res.json({
        success: false,
        code: 404,
        error: '用户不存在'
      });
    }
    
    const options = {
      courseId: courseId || null,
      type: type || null,
      startDate: startDate || null,
      endDate: endDate || null,
      limit: parseInt(limit) || 50,
      offset: parseInt(offset) || 0
    };
    
    const history = await UserService.getPracticeHistory(user._id, options);
    
    res.json({
      success: true,
      code: 0,
      data: history
    });
    
  } catch (error) {
    console.error('[User API] 获取练习历史失败:', error);
    res.json({
      success: false,
      code: 500,
      error: error.message || '获取练习历史失败'
    });
  }
});

// 获取指定用户错题数据
router.get('/error-words', async (req, res) => {
  try {
    const userId = req.query.userId || req.headers['x-openid'];
    if (!userId) {
      return res.json({ success: false, error: '缺少 userId 参数' });
    }
    // 查询数据库
    const result = await DatabaseService.getUserErrorWords(userId);
    res.json({ success: true, data: result });
  } catch (error) {
    console.error('[User API] 获取错题数据失败:', error);
    res.json({ success: false, error: error.message || '获取错题数据失败' });
  }
});

module.exports = router; 