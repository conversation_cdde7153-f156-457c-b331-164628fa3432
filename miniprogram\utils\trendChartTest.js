/**
 * 趋势图功能测试
 * 用于验证趋势图数据处理和绘制功能
 */

const TrendChartData = require('./trendChartData');

class TrendChartTest {
  
  /**
   * 生成测试数据
   */
  static generateTestData() {
    const testData = [];
    const today = new Date();
    
    // 生成过去7天的测试数据
    for (let i = 6; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(today.getDate() - i);
      
      // 模拟练习记录
      const practiceCount = Math.floor(Math.random() * 5) + 1; // 1-5次练习
      
      for (let j = 0; j < practiceCount; j++) {
        const record = {
          timestamp: new Date(date.getTime() + j * 3600000).toISOString(), // 每小时一次练习
          accuracy: Math.floor(Math.random() * 40) + 60, // 60-100%的正确率
          correctCount: Math.floor(Math.random() * 8) + 2, // 2-10个正确
          totalCount: 10, // 总共10个题目
          practiceTime: Math.floor(Math.random() * 300) + 180, // 3-8分钟
          type: 'normal_practice'
        };
        
        testData.push(record);
      }
    }
    
    return testData;
  }
  
  /**
   * 测试数据处理功能
   */
  static testDataProcessing() {
    console.log('=== 趋势图数据处理测试 ===');
    
    // 生成测试数据
    const testRecords = this.generateTestData();
    console.log('生成测试数据:', testRecords.length, '条记录');
    
    // 模拟存储到本地
    try {
      wx.setStorageSync('practiceHistory', testRecords);
      console.log('测试数据已存储到本地');
    } catch (error) {
      console.error('存储测试数据失败:', error);
      return false;
    }
    
    // 测试数据获取
    const trendData = TrendChartData.getTrendData(7);
    console.log('趋势图数据:', trendData);
    
    // 验证数据结构
    const requiredFields = ['labels', 'practiceCount', 'accuracy', 'totalTime', 'maxValues', 'summary'];
    const isValid = requiredFields.every(field => trendData.hasOwnProperty(field));
    
    if (isValid) {
      console.log('✅ 数据结构验证通过');
      console.log('📊 数据摘要:', trendData.summary);
      return true;
    } else {
      console.log('❌ 数据结构验证失败');
      return false;
    }
  }
  
  /**
   * 测试空数据处理
   */
  static testEmptyData() {
    console.log('=== 空数据处理测试 ===');
    
    // 清空本地数据
    try {
      wx.removeStorageSync('practiceHistory');
      console.log('已清空本地数据');
    } catch (error) {
      console.error('清空数据失败:', error);
    }
    
    // 测试空数据处理
    const emptyTrendData = TrendChartData.getTrendData(7);
    console.log('空数据趋势图:', emptyTrendData);
    
    // 验证空数据结构
    const isEmpty = emptyTrendData.summary.totalPractices === 0;
    const hasLabels = emptyTrendData.labels.length === 7;
    
    if (isEmpty && hasLabels) {
      console.log('✅ 空数据处理验证通过');
      return true;
    } else {
      console.log('❌ 空数据处理验证失败');
      return false;
    }
  }
  
  /**
   * 测试数据格式转换
   */
  static testDataConversion() {
    console.log('=== 数据格式转换测试 ===');
    
    // 测试不同格式的练习记录
    const mixedRecords = [
      // 新格式
      {
        timestamp: new Date().toISOString(),
        accuracy: 85,
        correctCount: 8,
        totalCount: 10,
        practiceTime: 300,
        type: 'normal_practice'
      },
      // 旧格式
      {
        date: new Date().toISOString(),
        statistics: {
          accuracy: 90,
          correctCount: 9,
          totalCount: 10
        },
        duration: 240
      }
    ];
    
    // 测试格式转换
    const convertedRecords = mixedRecords.map(record => 
      TrendChartData.convertPracticeRecord(record)
    );
    
    console.log('转换前:', mixedRecords);
    console.log('转换后:', convertedRecords);
    
    // 验证转换结果
    const isConverted = convertedRecords.every(record => 
      record.hasOwnProperty('timestamp') && 
      record.hasOwnProperty('accuracy') &&
      record.hasOwnProperty('correctCount')
    );
    
    if (isConverted) {
      console.log('✅ 数据格式转换验证通过');
      return true;
    } else {
      console.log('❌ 数据格式转换验证失败');
      return false;
    }
  }
  
  /**
   * 运行所有测试
   */
  static runAllTests() {
    console.log('🚀 开始运行趋势图功能测试...');
    
    const tests = [
      { name: '数据处理功能', test: () => this.testDataProcessing() },
      { name: '空数据处理', test: () => this.testEmptyData() },
      { name: '数据格式转换', test: () => this.testDataConversion() }
    ];
    
    let passedTests = 0;
    
    tests.forEach(({ name, test }) => {
      console.log(`\n--- 测试: ${name} ---`);
      try {
        const result = test();
        if (result) {
          passedTests++;
          console.log(`✅ ${name} 测试通过`);
        } else {
          console.log(`❌ ${name} 测试失败`);
        }
      } catch (error) {
        console.error(`💥 ${name} 测试异常:`, error);
      }
    });
    
    console.log(`\n📋 测试结果: ${passedTests}/${tests.length} 通过`);
    
    if (passedTests === tests.length) {
      console.log('🎉 所有测试通过！趋势图功能正常');
      return true;
    } else {
      console.log('⚠️ 部分测试失败，请检查相关功能');
      return false;
    }
  }
  
  /**
   * 清理测试数据
   */
  static cleanup() {
    try {
      wx.removeStorageSync('practiceHistory');
      console.log('🧹 测试数据已清理');
    } catch (error) {
      console.error('清理测试数据失败:', error);
    }
  }
}

module.exports = TrendChartTest;
