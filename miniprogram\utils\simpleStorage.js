/**
 * 简化存储管理 - 直接使用版本
 * 不考虑兼容性，直接使用最优化的数据结构
 */

class SimpleStorage {

  // 精简的存储键（仅保留必要的）
  static KEYS = {
    USER_DATA: 'userData',           // 用户信息 + 设置 + 教材
    ERROR_DATA: 'errorData',         // 错题记录
    CACHE: 'cache',                  // 缓存数据

    // **新增：统一管理的存储键**
    ERROR_WORDS: 'errorWords',       // 错题记录 
    CURRENT_TEXTBOOK: 'currentTextbook', // 当前教材
    PRACTICE_HISTORY: 'practiceHistory', // 练习历史
    LEARNING_PROGRESS: 'learningProgress', // 学习进度
    WORD_REWARDS: 'wordRewards',     // 字词奖励记录
    DAILY_TASKS_DATA: 'dailyTasksData',   // 每日任务数据
    UNLOCKED_ACHIEVEMENTS: 'unlockedAchievements', // 已解锁成就

    // 临时缓存数据
    LATEST_PRACTICE_RESULTS: 'latestPracticeResults',
    CACHED_PRACTICE_RESULTS: 'cachedPracticeResults',
    MANUAL_REVIEW_RESULTS: 'manualReviewResults',
    CORRECTION_RESULTS: 'correctionResults',

    // 其他配置
    SWITCH_TO_HISTORY_TAB: 'switchToHistoryTab',
    REDIRECT_AFTER_LOGIN: 'redirectAfterLogin',
    RANDOM_CHALLENGE_DATA: 'randomChallengeData',

    // 每日首次进入记录
    DAILY_FIRST_ENTRY: 'dailyFirstEntry',

    // 首次挑战同步状态
    FIRST_CHALLENGE_SYNC: 'firstChallengeSync',

    // **[New]** 新增教材数据存储键
    TEXTBOOK_DATA: 'textbookData'
  };

  // ========== 统一存储操作方法 ==========

  /**
   * 通用获取方法
   * @param {string} key 存储键名
   * @param {*} defaultValue 默认值
   * @returns {*} 存储的数据
   */
  static get(key, defaultValue = null) {
    try {
      return wx.getStorageSync(key) || defaultValue;
    } catch (error) {
      console.error(`[SimpleStorage] 获取存储失败 ${key}:`, error);
      return defaultValue;
    }
  }

  /**
   * 通用设置方法
   * @param {string} key 存储键名
   * @param {*} value 要存储的数据
   * @returns {boolean} 是否成功
   */
  static set(key, value) {
    try {
      wx.setStorageSync(key, value);
      console.log(`[SimpleStorage] 存储成功 ${key}`);
      return true;
    } catch (error) {
      console.error(`[SimpleStorage] 存储失败 ${key}:`, error);
      return false;
    }
  }

  /**
   * 删除存储项
   * @param {string} key 存储键名
   * @returns {boolean} 是否成功
   */
  static remove(key) {
    try {
      wx.removeStorageSync(key);
      console.log(`[SimpleStorage] 删除存储成功 ${key}`);
      return true;
    } catch (error) {
      console.error(`[SimpleStorage] 删除存储失败 ${key}:`, error);
      return false;
    }
  }

  // ========== 用户数据管理 ==========

  /**
   * 获取用户数据（包含个人信息、设置、当前教材）
   */
  static getUserData() {
    return wx.getStorageSync(this.KEYS.USER_DATA) || {
      // 基本信息
      openid: '',
      nickname: '匿名用户',
      avatar: '',
      userType: 'guest', // 保留：区分微信用户和游客

      // 累计练习时间（秒）
      totalTime: 0,

      // 统计数据（直接存储，避免重复计算）
      accuracyRate: 0,      // 正确率
      consecutiveDays: 0,   // 连续天数
      lastLoginDate: '',    // **新增：上次登录日期**

      // **新增：成就点数**
      achievementPoints: 0,

      // 每日挑战记录
      lastChallengeDate: '',        // 最后一次课程挑战日期
      lastErrorCorrectionDate: '',  // 最后一次错题订正日期
      lastRandomChallengeDate: '',  // 最后一次随机挑战日期
      lastPerfectScoreDate: '',     // 最后一次满分挑战日期

      // 登录历史记录
      loginHistory: [],

      // 时间戳
      createTime: new Date().toISOString(),
      updateTime: new Date().toISOString()
    };
  }

  /**
   * **[New]** 保存核心用户数据（不含教材数据）
   */
  static saveUserData(userData) {
    try {
      const dataToSave = { ...userData };
      
      // **防御性编程：确保不保存任何与教材相关的字段到userData**
      delete dataToSave.points;
      delete dataToSave.experience;
      delete dataToSave.level;
      delete dataToSave.title;
      delete dataToSave.textbookData;
      
      wx.setStorageSync(this.KEYS.USER_DATA, dataToSave);
      this.updateDataModifiedTime('userData');
      return true;
    } catch (e) {
      console.error('[SimpleStorage] 保存用户数据失败', e);
      return false;
    }
  }

  /**
   * **[New]** 获取所有教材数据
   */
  static getTextbookData() {
    return wx.getStorageSync(this.KEYS.TEXTBOOK_DATA) || {};
  }

  /**
   * **[New]** 保存所有教材数据
   */
  static setTextbookData(textbookData) {
    try {
      wx.setStorageSync(this.KEYS.TEXTBOOK_DATA, textbookData);
      this.updateDataModifiedTime('textbookData');
 
      return true;
    } catch (e) {
      console.error('[SimpleStorage] 保存教材数据失败', e);
      return false;
    }
  }

  /**
   * 获取教材唯一标识
   * @param {Object} textbook 教材对象
   * @returns {string} 教材标识
   */
  static getTextbookKey(textbook) {
    if (!textbook) return 'unknown';
    
    const publisherId = textbook.publisher?.id || textbook.publisher?.name || 'unknown';
    const gradeId = textbook.grade?.id || textbook.grade?.name || 'unknown';
    const term = textbook.term || 'unknown';
    
    return `${publisherId}_${gradeId}_${term}`;
  }
  
  /**
   * **[CORRECTED]** 获取当前激活教材的数据 (This is the one, true implementation)
   */
  static getCurrentTextbookData() {
    const allTextbookData = this.getTextbookData();
    const currentTextbook = this.getCurrentTextbook();
    
    console.log('🔍 [getCurrentTextbookData] 调试信息:', {
      allTextbookData存在: !!allTextbookData,
      allTextbookData键数量: Object.keys(allTextbookData || {}).length,
      currentTextbook: currentTextbook,
      hasPublisher: !!(currentTextbook && currentTextbook.publisher)
    });
    
    if (!currentTextbook || !currentTextbook.publisher) {
      console.log('🔍 [SimpleStorage] 当前没有选择教材，无法获取教材数据');
      console.log('🔍 [SimpleStorage] currentTextbook详情:', currentTextbook);
      return null;
    }
    
    const textbookKey = this.getTextbookKey(currentTextbook);
    const textbookData = allTextbookData[textbookKey] || null;

    console.log('🔍 [getCurrentTextbookData] 教材数据查找结果:', {
      textbookKey: textbookKey,
      找到教材数据: !!textbookData,
      可用存储键: Object.keys(allTextbookData || {})
    });

    if (!textbookData) {
      console.log(`[SimpleStorage] 未找到教材[${textbookKey}]的数据，返回null`);
      console.log(`[SimpleStorage] 提示：可调用 SimpleStorage.updateCurrentTextbookData(SimpleStorage.getDefaultTextbookData()) 初始化`);
    }

    return textbookData;
  }
  
  /**
   * 更新当前教材的用户数据
   * @param {Object} updates 要更新的数据
   * @returns {boolean} 是否成功
   */
  static updateCurrentTextbookData(updates) {
    const allTextbookData = this.getTextbookData();
    const currentTextbook = this.getCurrentTextbook();

    if (!currentTextbook || !currentTextbook.publisher) {
      console.log('🔍 [SimpleStorage] 当前没有选择教材，无法更新教材数据');
      return false;
    }

    const textbookKey = this.getTextbookKey(currentTextbook);
    const existingData = allTextbookData[textbookKey] || this.getDefaultTextbookData();
    
    allTextbookData[textbookKey] = {
      ...existingData,
      ...updates,
      updateTime: new Date().toISOString()
    };
    
    // 自动更新等级和称号
    if (updates.experience !== undefined) {
      const app = getApp();
      if (app && app.calculateUserTitleByExperience) {
        const experience = allTextbookData[textbookKey].experience;
        const titleInfo = app.calculateUserTitleByExperience(experience);
        allTextbookData[textbookKey].level = titleInfo.level;
        allTextbookData[textbookKey].title = titleInfo.title;
        console.log(`🎖️ [SimpleStorage] 更新等级称号: ${titleInfo.level} - ${titleInfo.title}`);
      }
    }

    return this.setTextbookData(allTextbookData);
  }

  /**
   * 获取指定教材下已出现过的课程ID集合（根据本地学习进度推断）
   */
  static getCourseIdsOfTextbook(textbook) {
    if (!textbook || !textbook.publisher) return [];
    const prefix = `${textbook.publisher.id}_${textbook.grade.id}_${textbook.term}_`;
    const learningProgress = this.getLearningProgress();
    const set = new Set();
    Object.keys(learningProgress).forEach(courseKey => {
      if (courseKey.startsWith(prefix)) {
        const parts = courseKey.split('_');
        if (parts.length === 4) set.add(parts[3]);
      }
    });
    return Array.from(set);
  }

  /**
   * 获取当前教材的字词奖励记录（兼容 course 级别存储）
   */
  static getCurrentTextbookWordRewards() {
    const currentTextbook = this.getCurrentTextbook();
    if (!currentTextbook) return {};
    // ② 获取当前教材下所有课程 ID（通过学习进度推断）
    const courseIds = this.getCourseIdsOfTextbook(currentTextbook);
    // ③ 汇总每课的 wordRewards_<courseId>（兼容旧版本）
    const merged = {  }; // 从教材级别数据开始
    courseIds.forEach(cid => {
      const perCourseRewards = wx.getStorageSync(`wordRewards_${cid}`) || {};
      // 合并课程级别数据，但不覆盖教材级别已有的数据
      Object.keys(perCourseRewards).forEach(word => {
        if (!merged[word]) {
          merged[word] = perCourseRewards[word];
        }
      });
    });
    
    console.log('[getCurrentTextbookWordRewards] 最终汇总结果:', {
      课程级别字词数: courseIds.reduce((sum, cid) => {
        const courseRewards = wx.getStorageSync(`wordRewards_${cid}`) || {};
        return sum + Object.keys(courseRewards).length;
      }, 0),
      合并后总数: Object.keys(merged).length
    });
    
    return merged;
  }

   

  
 

  
  

 

  // ========== 练习数据管理 ==========

  /**
   * 添加练习记录
   */
  static addPracticeRecord(record) {
    console.log("🔥 [addPracticeRecord] 添加练习记录 - 原始数据:", record)
    
    // **添加详细的数据检查**
    console.log("🔥 [addPracticeRecord] 数据检查:", {
      有words数组: !!(record.words && record.words.length > 0),
      words数量: record.words ? record.words.length : 0,
      有results数组: !!(record.results && record.results.length > 0),
      results数量: record.results ? record.results.length : 0,
      有courseInfo: !!record.courseInfo,
      courseInfo详情: record.courseInfo,
      有统计信息: !!record.statistics,
      统计详情: record.statistics
    })
    // 标准化记录格式（只保留核心字段）
    const standardRecord = {
      id: new Date().toISOString(),
      timestamp: new Date().toISOString(),
      type: record.type || 'normal',
      courseInfo: record.courseInfo || null, // 课程信息（用于教材过滤）
      statistics: {
        totalCount: record.statistics?.totalCount || record.totalCount || 0,
        correctCount: record.statistics?.correctCount || record.correctCount || 0,
        accuracy: record.statistics?.accuracy || record.accuracy || 0
      },
      // 只保留字词和结果，删除大数据字段
      words: (record.words || []).map((word, index) => ({
        word: word.word,
        result: record.results ? record.results[index] : (word.result || word.isCorrect || false)
      }))
      // 删除: canvasStates, challengeRewards 等大数据字段
    };

    // === 修复：确保 statistics.totalCount 有效 ===
    if (standardRecord.statistics.totalCount === 0 && standardRecord.words.length > 0) {
      standardRecord.statistics.totalCount = standardRecord.words.length;
      // 若 correctCount+错误数可得出统计，也一并计算准确率
      const correct = standardRecord.words.filter(w => w.result === true).length;
      const total = standardRecord.statistics.totalCount;
      standardRecord.statistics.correctCount = correct;
      standardRecord.statistics.accuracy = Math.round((correct / total) * 100);
      console.log('🩹 [addPracticeRecord] 自动补全 statistics.totalCount，确保准确率计算正常。', standardRecord.statistics);
    }

    // 添加到practiceHistory并限制数量
    const practiceHistory = wx.getStorageSync('practiceHistory') || [];
    practiceHistory.unshift(standardRecord);
    if (practiceHistory.length > 100) {
      practiceHistory.splice(100); // 限制数量
    }
    //wx.setStorageSync('practiceHistory', practiceHistory);
    this.setPracticeHistory(practiceHistory)
    console.log('🔥 [addPracticeRecord] 练习记录已添加，当前总数:', practiceHistory.length);
    console.log('🔥 [addPracticeRecord] 标准化后的记录:', standardRecord);
  }

  /**
   * 获取练习记录
   */
  static getPracticeRecords(limit = null) {
    const practiceHistory = wx.getStorageSync('practiceHistory') || [];
    return limit ? practiceHistory.slice(0, limit) : practiceHistory;
  }

  // ========== 错题数据管理 ==========

  /**
   * 获取错题列表
   * @returns {Array} 错题列表
   */
  static getErrorWords() {
    return this.get(this.KEYS.ERROR_WORDS, []);
  }

  


  static getManualReviewResults() {
    return this.get(this.KEYS.MANUAL_REVIEW_RESULTS, null)
  }
  static setManualReviewResults(results) {
    return this.set(this.KEYS.MANUAL_REVIEW_RESULTS, results)
  }
  static removeManualReviewResults() {
    return this.remove(this.KEYS.MANUAL_REVIEW_RESULTS)
  }
  /**
   * 保存错题列表
   * @param {Array} errorWords 错题列表
   * @returns {boolean} 是否成功
   */
  static setErrorWords(errorWords) {
    const result = this.set(this.KEYS.ERROR_WORDS, errorWords);
    if (result) {
      this.updateDataModifiedTime('errorWords');
    }
    return result;
  }

  /**
   * 添加单个错题
   * @param {Object} errorWord 错题对象
   * @returns {boolean} 是否成功
   */
  static addErrorWord(errorWord) {
    try {
      const errorWords = this.getErrorWords();
      errorWords.push(errorWord);
      return this.setErrorWords(errorWords);
    } catch (error) {
      console.error('[SimpleStorage] 添加错题失败:', error);
      return false;
    }
  }

  /**
   * 标记错题为已订正
   * @param {string} wordId 错题ID
   * @returns {boolean} 是否成功
   */
  static markErrorWordCorrected(wordId) {
    try {
      const errorWords = this.getErrorWords();
      const wordIndex = errorWords.findIndex(error => error.id === wordId);

      if (wordIndex >= 0) {
        errorWords[wordIndex].corrected = true;
        errorWords[wordIndex].correctedTime = new Date().toISOString();
        return this.setErrorWords(errorWords);
      }
      return false;
    } catch (error) {
      console.error('[SimpleStorage] 标记错题订正失败:', error);
      return false;
    }
  }

  /**
   * 清空错题记录
   * @returns {boolean} 是否成功
   */
  static clearErrorWords() {
    return this.set(this.KEYS.ERROR_WORDS, []);
  }

  // ========== 教材管理 ==========

  /**
   * 获取当前教材
   * @returns {Object} 当前教材对象
   */
  static getCurrentTextbook() {
    return this.get(this.KEYS.CURRENT_TEXTBOOK, {});
  }

  /**
   * 设置当前教材
   * @param {Object} textbook 教材对象
   * @returns {boolean} 是否成功
   */
  static setCurrentTextbook(textbook) {
    const result = this.set(this.KEYS.CURRENT_TEXTBOOK, textbook);
    if (result) {
      this.updateDataModifiedTime('currentTextbook');
    }
    return result;
  }

  // ========== 练习历史管理 ==========

  /**
   * 获取练习历史
   * @returns {Array} 练习历史列表
   */
  static getPracticeHistory() {
    return this.get(this.KEYS.PRACTICE_HISTORY, []);
  }

  /**
   * 设置练习历史
   * @param {Array} history 练习历史列表
   * @returns {boolean} 是否成功
   */
  static setPracticeHistory(history) {
    const result = this.set(this.KEYS.PRACTICE_HISTORY, history);
    if (result) {
      this.updateDataModifiedTime('practiceHistory');
    }
    return result;
  }

  /**
   * 获取练习历史基础数据（用于同步）
   * @returns {Array} 练习历史基础数据
   */
  static getPracticeHistoryBase() {
    try {
      return wx.getStorageSync('practiceHistoryBase') || [];
    } catch (error) {
      console.error('[SimpleStorage] 获取练习历史基础数据失败:', error);
      return [];
    }
  }

  /**
   * 设置练习历史基础数据（用于同步）
   * @param {Array} data 练习历史基础数据
   * @returns {boolean} 是否成功
   */
  static setPracticeHistoryBase(data) {
    try {
      wx.setStorageSync('practiceHistoryBase', data);
      return true;
    } catch (error) {
      console.error('[SimpleStorage] 设置练习历史基础数据失败:', error);
      return false;
    }
  }

  /**
   * 添加练习历史记录（标准化格式，用于同步到后端）
   * @param {Object} record 练习记录
   * @returns {boolean} 是否成功
   */
  static addPracticeHistory(record) {
    try {
      console.log('🔍 [addPracticeHistory] 开始添加练习历史记录');
      console.log('🔍 [addPracticeHistory] 原始记录:', record);
      
      // 标准化记录格式，确保与后端数据库表结构一致
      const standardizedRecord = this.standardizePracticeRecord(record);
      console.log('🔍 [addPracticeHistory] 标准化后的记录:', standardizedRecord);
      
      // 添加到基础数据（用于同步）
      const history = this.getPracticeHistoryBase();
      history.push(standardizedRecord);
      
      // 限制数量，避免数据过大
      if (history.length > 1000) {
        history.splice(0, history.length - 1000);
      }
      
      const result = this.setPracticeHistoryBase(history);
      console.log('🔍 [addPracticeHistory] 保存结果:', result, '当前历史记录数量:', history.length);
      return result;
    } catch (error) {
      console.error('[SimpleStorage] 添加练习历史记录失败:', error);
      return false;
    }
  }

  /**
   * 标准化练习记录格式（与后端数据库表结构一致）
   * @param {Object} record 原始练习记录
   * @returns {Object} 标准化后的记录
   */
  static standardizePracticeRecord(record) {
    console.log('🔍 [standardizePracticeRecord] 开始标准化练习记录');
    console.log('🔍 [standardizePracticeRecord] 原始记录:', record);
    
    // 提取基本信息
    const {
      timestamp = new Date().toISOString(),
      duration = 0,
      words = [],
      results = [],
      courseInfo = null,
      statistics = {},
      type = 'normal'
    } = record;

    console.log('🔍 [standardizePracticeRecord] 提取的type字段:', type);

    // 计算统计数据
    const totalCount = statistics.totalCount || words.length || 0;
    const correctCount = statistics.correctCount || 
      (results.length > 0 ? results.filter(r => r === true).length : 0) ||
      (words.length > 0 ? words.filter(w => w.result === true).length : 0);
    const accuracy = totalCount > 0 ? Math.round((correctCount / totalCount) * 100) : 0;
    
    // 修复：确保 practiceTime 有有效值，如果 duration 为 0 或无效，使用默认值
    let practiceTime = 0;
    if (duration && duration > 0) {
      practiceTime = Math.round(duration / 1000); // 转换为秒
    } else {
      // 如果 duration 无效，尝试从原始记录中获取
      const originalDuration = record.duration || record.statistics?.duration || 0;
      if (originalDuration && originalDuration > 0) {
        practiceTime = Math.round(originalDuration / 1000);
      } else {
        // 如果都没有，使用默认值（至少1秒）
        practiceTime = 1;
      }
    }

    console.log('🔍 [standardizePracticeRecord] 统计数据计算:', {
      statistics: statistics,
      wordsLength: words.length,
      resultsLength: results.length,
      resultsValues: results,
      totalCount: totalCount,
      correctCount: correctCount,
      accuracy: accuracy,
      practiceTime: practiceTime
    });

    // 确定练习类型
    let practiceType = type;
    console.log('🔍 [standardizePracticeRecord] 原始type:', type);
    
    if (type === 'normal') practiceType = 'normal_practice';
    else if (type === 'error') practiceType = 'error_correction';
    else if (type === 'challenge') practiceType = 'error_challenge';
    else if (type === 'random') practiceType = 'random_challenge';
    // 新增：处理标准化的类型值
    else if (type === 'normal_practice') practiceType = 'normal_practice';
    else if (type === 'error_correction') practiceType = 'error_correction';
    else if (type === 'error_challenge') practiceType = 'error_challenge';
    else if (type === 'random_challenge') practiceType = 'random_challenge';
    else practiceType = 'normal_practice'; // 默认值
    
    console.log('🔍 [standardizePracticeRecord] 转换后的practiceType:', practiceType);

    // 确定课程ID和课程信息（根据练习类型）
    let courseId = 'unknown';
    let finalCourseInfo = courseInfo;
    
    if (courseInfo) {
      if (practiceType === 'error_correction') {
        // 错题订正涉及多个课程，使用特殊标识
        courseId = 'error_correction_multi_course';
        
        // 构建多课程信息
        finalCourseInfo = {
          ...courseInfo,
          isErrorCorrection: true,
          courseId: 'error_correction_multi_course',
          courseName: '错题订正',
          courseTitle: '错题订正练习',
          publisher: '错题集',
          grade: '复习',
          involvedCourses: courseInfo.involvedCourses || [],
          originalCourseInfo: courseInfo // 保留原始课程信息
        };
        
        console.log('🔍 [standardizePracticeRecord] 错题订正 - 多课程信息:', finalCourseInfo);
      } else if (practiceType === 'random_challenge') {
        // 随机挑战使用特殊标识
        courseId = 'random_challenge';
        
        finalCourseInfo = {
          ...courseInfo,
          isRandomChallenge: true,
          courseId: 'random_challenge',
          courseName: '随机挑战',
          courseTitle: courseInfo.courseTitle || '随机挑战',
          publisher: '随机挑战',
          grade: '挑战'
        };
        
        console.log('🔍 [standardizePracticeRecord] 随机挑战 - 课程信息:', finalCourseInfo);
      } else if (practiceType === 'error_challenge') {
        // 错题挑战使用特殊标识
        courseId = 'error_challenge';
        
        finalCourseInfo = {
          ...courseInfo,
          isErrorChallenge: true,
          courseId: 'error_challenge',
          courseName: '错题挑战',
          courseTitle: courseInfo.courseTitle || '错题挑战',
          publisher: '错题集',
          grade: '挑战'
        };
        
        console.log('🔍 [standardizePracticeRecord] 错题挑战 - 课程信息:', finalCourseInfo);
      } else if (courseInfo.courseId) {
        courseId = courseInfo.courseId;
      } else if (courseInfo.publisherId && courseInfo.gradeId && courseInfo.term) {
        courseId = `${courseInfo.publisherId}_${courseInfo.gradeId}_${courseInfo.term}`;
      }
    }

    // 返回标准化记录（与后端数据库表结构一致）
    const standardizedRecord = {
      course_id: courseId,
      type: practiceType,
      timestamp: timestamp,
      accuracy: accuracy,
      correctCount: correctCount,
      totalCount: totalCount,
      practiceTime: practiceTime,
      // 保留原始数据用于前端显示
      originalRecord: {
        words: words,
        results: results,
        courseInfo: finalCourseInfo, // 使用最终的课程信息
        duration: duration
      }
    };
    
    console.log('🔍 [standardizePracticeRecord] 最终标准化记录:', standardizedRecord);
    return standardizedRecord;
  }

  /**
   * 清空练习历史基础数据（同步后调用）
   * @returns {boolean} 是否成功
   */
  static clearPracticeHistoryBase() {
    try {
      wx.removeStorageSync('practiceHistoryBase');
      return true;
    } catch (error) {
      console.error('[SimpleStorage] 清空练习历史基础数据失败:', error);
      return false;
    }
  }

  /**
   * 获取上次同步时间戳
   * @returns {number} 上次同步时间戳
   */
  static getLastSyncTime() {
    try {
      return wx.getStorageSync('lastSyncTime') || 0;
    } catch (error) {
      console.error('[SimpleStorage] 获取上次同步时间失败:', error);
      return 0;
    }
  }

  /**
   * 设置上次同步时间戳
   * @param {number} timestamp 同步时间戳
   * @returns {boolean} 是否成功
   */
  static setLastSyncTime(timestamp) {
    try {
      wx.setStorageSync('lastSyncTime', timestamp);
      return true;
    } catch (error) {
      console.error('[SimpleStorage] 设置上次同步时间失败:', error);
      return false;
    }
  }

  // ========== 学习进度管理 ==========

  /**
   * 获取学习进度
   * @returns {Object} 学习进度对象
   */
  static getLearningProgress() {
    return this.get(this.KEYS.LEARNING_PROGRESS, {});
  }

  /**
   * 设置学习进度
   * @param {Object} progress 学习进度对象
   * @returns {boolean} 是否成功
   */
  static setLearningProgress(progress) {
    const result = this.set(this.KEYS.LEARNING_PROGRESS, progress);
    if (result) {
      this.updateDataModifiedTime('learningProgress');
    }
    return result;
  }

  /**
   * 更新特定课程的学习进度
   * @param {string} courseId 课程ID
   * @param {Object} courseProgress 课程进度
   * @returns {boolean} 是否成功
   */
  static updateCourseProgress(courseId, courseProgress) {
    try {
      const progress = this.getLearningProgress();
      progress[courseId] = courseProgress;
      return this.setLearningProgress(progress);
    } catch (error) {
      console.error('[SimpleStorage] 更新课程进度失败:', error);
      return false;
    }
  }

  // ========== 每日任务数据管理 ==========

  /**
   * 获取每日任务数据
   * @returns {Object} 每日任务数据
   */
  static getDailyTasksData() {
    return this.get(this.KEYS.DAILY_TASKS_DATA, {});
  }

  /**
   * 设置每日任务数据
   * @param {Object} data 每日任务数据
   * @returns {boolean} 是否成功
   */
  static setDailyTasksData(data) {
    // 限制数据只保留最近五天
    const limitedData = this.limitDailyTasksData(data);

    // 更新修改时间（用于同步功能）
    const now = new Date().toISOString();
    this.set('dailyTasksData_lastModified', now);

    return this.set(this.KEYS.DAILY_TASKS_DATA, limitedData);
  }

  /**
   * 限制每日任务数据只保留最近五天
   * @param {Object} data 原始每日任务数据
   * @returns {Object} 限制后的数据
   */
  static limitDailyTasksData(data) {
    if (!data || typeof data !== 'object') {
      return {};
    }

    try {
      // 获取所有日期并排序
      const allDates = Object.keys(data);
      if (allDates.length <= 5) {
        return data; // 如果数据量不超过5天，直接返回
      }

      // 按日期排序（降序，最新的在前面）
      const sortedDates = allDates.sort((a, b) => new Date(b) - new Date(a));

      // 只保留最近5天的数据
      const recentDates = sortedDates.slice(0, 5);

      const limitedData = {};
      recentDates.forEach(date => {
        limitedData[date] = data[date];
      });

      console.log(`[SimpleStorage] 每日任务数据已限制为最近5天，从 ${allDates.length} 天减少到 ${recentDates.length} 天`);

      return limitedData;
    } catch (error) {
      console.error('[SimpleStorage] 限制每日任务数据失败:', error);
      return data; // 出错时返回原始数据
    }
  }

  // **[DEFINITIVE_FIX]** Restoring all missing functions inside the class definition.
  static updateUserStats(stats) {
    console.log('🔥 [SimpleStorage.updateUserStats] 被调用，参数:', stats);
    
    const userData = this.getUserData();
    if (!userData || !userData.openid) {
      console.log('[SimpleStorage] 无有效用户数据，跳过统计更新:', stats);
      return false;
    }

    console.log('🔥 [SimpleStorage.updateUserStats] 当前用户数据:', userData);

    let textbookDataUpdated = false;
    const textbookUpdates = {};

    // 累加练习次数和练习字数
    if (stats.practiceCount) {
      userData.practiceCount = (userData.practiceCount || 0) + stats.practiceCount;
      console.log('🔥 [SimpleStorage.updateUserStats] 更新练习次数:', userData.practiceCount);
    }
    if (stats.wordsPracticed) {
      userData.wordsPracticed = (userData.wordsPracticed || 0) + stats.wordsPracticed;
      console.log('🔥 [SimpleStorage.updateUserStats] 更新练习字数:', userData.wordsPracticed);
    }

    // **修复：支持秒级统计，优先使用 practiceTimeSeconds，兼容旧字段 practiceTime（分钟）**
    let practiceTimeInSeconds = 0;
    if (stats.practiceTimeSeconds !== undefined) {
      practiceTimeInSeconds = stats.practiceTimeSeconds; // 已经是秒

      // 同步更新旧字段 practiceTime（分钟），保留一位小数提高精度
      const minutes = practiceTimeInSeconds / 60;
      userData.practiceTime = (userData.practiceTime || 0) + minutes;
    } else if (stats.practiceTime) {
      // 旧字段：分钟
      practiceTimeInSeconds = stats.practiceTime * 60;
      userData.practiceTime = (userData.practiceTime || 0) + stats.practiceTime;
    }

    if (practiceTimeInSeconds > 0) {
      const currentTextbookData = this.getCurrentTextbookData();
      textbookUpdates.totalTime = (currentTextbookData?.totalTime || 0) + practiceTimeInSeconds;
      textbookDataUpdated = true;
      console.log('🔥 [SimpleStorage.updateUserStats] 更新练习时间:', {
        practiceTimeInSeconds,
        beforeSeconds: currentTextbookData?.totalTime || 0,
        afterSeconds: textbookUpdates.totalTime
      });
    }
    
    // 累加正确和错误数量
    if (stats.correctCount) {
      userData.correctCount = (userData.correctCount || 0) + stats.correctCount;
    }
    if (stats.wrongCount) {
      userData.wrongCount = (userData.wrongCount || 0) + stats.wrongCount;
    }

    // 更新准确率和连续天数 (这些是全局统计)
    if (stats.accuracyRate !== undefined) {
      userData.accuracyRate = stats.accuracyRate;
    }
    if (stats.consecutiveDays !== undefined) {
      userData.consecutiveDays = stats.consecutiveDays;
    }

    // 处理与教材绑定的数据：积分和经验
    if (stats.points) {
      textbookUpdates.points = (this.getCurrentTextbookData()?.points || 0) + stats.points;
      textbookDataUpdated = true;
    }
    if (stats.experience) {
      textbookUpdates.experience = (this.getCurrentTextbookData()?.experience || 0) + stats.experience;
      textbookDataUpdated = true;
    }
    
    // 如果有教材数据更新，则执行
    if (textbookDataUpdated) {
      console.log('🔥 [SimpleStorage.updateUserStats] 准备更新教材数据:', textbookUpdates);
      this.updateCurrentTextbookData(textbookUpdates);
      console.log('🔥 [SimpleStorage.updateUserStats] 教材数据更新完成');
    }
    
    console.log('🔥 [SimpleStorage.updateUserStats] 更新后的核心用户统计数据:', userData);
    return this.saveUserData(userData);
  }

  static calculateAndUpdateConsecutiveDays() {
    try {
      const userData = this.getUserData();
      if (!userData || !userData.openid) return 0;

      const lastLoginDate = userData.lastLoginDate;
      const today = new Date().toISOString().split('T')[0];

      if (!lastLoginDate) {
        // 首次使用，连续天数为1
        userData.consecutiveDays = 1;
      } else {
        const lastDate = new Date(lastLoginDate);
        const todayDate = new Date(today);
        
        // 修正：确保日期在同一时区进行比较
        lastDate.setHours(0, 0, 0, 0);
        todayDate.setHours(0, 0, 0, 0);

        const diffTime = todayDate - lastDate;
        const diffDays = diffTime / (1000 * 60 * 60 * 24);

        if (diffDays === 1) {
          // 昨天登录过，连续天数+1
          userData.consecutiveDays = (userData.consecutiveDays || 0) + 1;
        } else if (diffDays > 1) {
          // 登录中断，重置为1
          userData.consecutiveDays = 1;
        }
        // 如果diffDays <= 0（即当天重复登录），则不做任何操作
      }

      userData.lastLoginDate = today;
      this.saveUserData(userData);
      
      console.log(`[SimpleStorage] 连续天数更新: ${userData.consecutiveDays}, 上次登录: ${lastLoginDate}, 今天: ${today}`);
      return userData.consecutiveDays;
    } catch (error) {
      console.error('[SimpleStorage] 计算连续天数失败:', error);
      return 0;
    }
  }

  static updateChallengeDate(challengeType, date = null) {
    const today = date || new Date().toISOString().split('T')[0];
    const userData = this.getUserData();
    if (!userData || !userData.openid) {
      console.log('[SimpleStorage] 无有效用户数据，跳过挑战日期更新:', challengeType);
      return false;
    }
    const dateKeyMap = {
      course_challenge: 'lastChallengeDate',
      error_correction: 'lastErrorCorrectionDate',
      random_challenge: 'lastRandomChallengeDate',
      perfect_score: 'lastPerfectScoreDate',
    };
    if (dateKeyMap[challengeType]) {
      userData[dateKeyMap[challengeType]] = today;
    } else {
      console.warn('[SimpleStorage] 未知的挑战类型:', challengeType);
      return false;
    }
    const success = this.saveUserData(userData);
    if (success) {
      console.log(`[SimpleStorage] 更新${challengeType}日期:`, today);
    }
    return success;
  }

  static isChallengeCompletedToday(challengeType) {
    const today = new Date().toISOString().split('T')[0];
    const userData = this.getUserData();
    const dateKeyMap = {
      course_challenge: 'lastChallengeDate',
      error_correction: 'lastErrorCorrectionDate',
      random_challenge: 'lastRandomChallengeDate',
      perfect_score: 'lastPerfectScoreDate',
    };
    return dateKeyMap[challengeType] ? userData[dateKeyMap[challengeType]] === today : false;
  }

  static calculateAndUpdateAccuracyRate() {
    try {
      const practiceHistory = wx.getStorageSync('practiceHistory') || [];
      if (practiceHistory.length === 0) {
        this.updateUserStats({ accuracyRate: 0 });
        return 0;
      }
      let totalCorrect = 0;
      let totalAttempts = 0;
      practiceHistory.forEach(record => {
        if (record.statistics) {
          totalCorrect += record.statistics.correctCount || 0;
          totalAttempts += record.statistics.totalCount || 0;
        }
      });
      const accuracy = totalAttempts > 0 ? Math.round((totalCorrect / totalAttempts) * 100) : 0;
      this.updateUserStats({ accuracyRate: accuracy });
      console.log('[SimpleStorage] 正确率计算完成:', accuracy + '%');
      return accuracy;
    } catch (error) {
      console.error('[SimpleStorage] 计算正确率失败:', error);
      return 0;
    }
  }
 

  static getRandomChallengeData() {
    return this.get('randomChallengeData', null);
  }

  static removeRandomChallengeData() {
    try {
      wx.removeStorageSync('randomChallengeData');
    } catch (error) {
      console.error('删除随机挑战数据失败:', error);
    }
  }

  // **[DEFINITIVE_FIX_2]** Restoring getDefaultTextbookData and correcting the logic of updateCurrentTextbookData.
  static getDefaultTextbookData() {
    return {
      points: 0,
      experience: 0,
      level: 1,
      title: '新手上路',
      totalTime: 0,
      lastChallengeDate: '',
      lastErrorCorrectionDate: '',
      lastRandomChallengeDate: '',
      lastPerfectScoreDate: '',
      createTime: new Date().toISOString(),
      updateTime: new Date().toISOString()
    };
  }

  // ========== 成就管理 ==========

  /**
   * 获取已解锁成就
   * @returns {Object} 已解锁成就对象
   */
  static getUnlockedAchievements() {
    return this.get(this.KEYS.UNLOCKED_ACHIEVEMENTS, {});
  }

  /**
   * 设置已解锁成就
   * @param {Object} achievements 已解锁成就对象
   * @returns {boolean} 是否成功
   */
  static setUnlockedAchievements(achievements) {
    return this.set(this.KEYS.UNLOCKED_ACHIEVEMENTS, achievements);
  }

  // ========== 临时缓存数据管理 ==========

  /**
   * 获取最新练习结果
   * @returns {Object} 最新练习结果
   */
  static getLatestPracticeResults() {
    return this.get(this.KEYS.LATEST_PRACTICE_RESULTS, null);
  }

  /**
   * 设置最新练习结果
   * @param {Object} results 练习结果
   * @returns {boolean} 是否成功
   */
  static setLatestPracticeResults(results) {
    return this.set(this.KEYS.LATEST_PRACTICE_RESULTS, results);
  }

  /**
   * 清除最新练习结果
   * @returns {boolean} 是否成功
   */
  static clearLatestPracticeResults() {
    return this.remove(this.KEYS.LATEST_PRACTICE_RESULTS);
  }

  /**
   * 获取缓存的练习结果
   * @returns {Object} 缓存的练习结果
   */
  static getCachedPracticeResults() {
    return this.get(this.KEYS.CACHED_PRACTICE_RESULTS, null);
  }

  /**
   * 设置缓存的练习结果
   * @param {Object} results 练习结果
   * @returns {boolean} 是否成功
   */
  static setCachedPracticeResults(results) {
    return this.set(this.KEYS.CACHED_PRACTICE_RESULTS, results);
  }

  /**
   * 清除缓存的练习结果
   * @returns {boolean} 是否成功
   */
  static clearCachedPracticeResults() {
    return this.remove(this.KEYS.CACHED_PRACTICE_RESULTS);
  }

  // ========== 配置标记管理 ==========

  /**
   * 设置切换到历史记录标签的标记
   * @param {boolean} shouldSwitch 是否切换
   * @returns {boolean} 是否成功
   */
  static setSwitchToHistoryTab(shouldSwitch) {
    if (shouldSwitch) {
      return this.set(this.KEYS.SWITCH_TO_HISTORY_TAB, true);
    } else {
      return this.remove(this.KEYS.SWITCH_TO_HISTORY_TAB);
    }
  }

  /**
   * 检查是否需要切换到历史记录标签
   * @returns {boolean} 是否需要切换
   */
  static shouldSwitchToHistoryTab() {
    const result = this.get(this.KEYS.SWITCH_TO_HISTORY_TAB, false);
    if (result) {
      this.remove(this.KEYS.SWITCH_TO_HISTORY_TAB); // 使用后清除
    }
    return result;
  }

  /**
   * 设置登录后重定向页面
   * @param {string} page 页面路径
   * @returns {boolean} 是否成功
   */
  static setRedirectAfterLogin(page) {
    return this.set(this.KEYS.REDIRECT_AFTER_LOGIN, page);
  }

  /**
   * 获取并清除登录后重定向页面
   * @returns {string|null} 页面路径
   */
  static getAndClearRedirectAfterLogin() {
    const page = this.get(this.KEYS.REDIRECT_AFTER_LOGIN, null);
    if (page) {
      this.remove(this.KEYS.REDIRECT_AFTER_LOGIN);
    }
    return page;
  }

  // ========== 数据同步支持方法 ==========

  /**
   * 更新数据修改时间（用于数据同步）
   * @param {string} dataType 数据类型
   */
  static updateDataModifiedTime(dataType) {
    try {
      const now = new Date().toISOString();
      this.set(`${dataType}_lastModified`, now);
    } catch (error) {
      console.error('[SimpleStorage] 更新数据修改时间失败:', error);
    }
  }

  // ========== 批量操作和迁移方法 ==========

 

  /**
   * 获取所有存储统计信息
   * @returns {Object} 存储统计
   */
  static getStorageStatistics() {
    try {
      const stats = {
        errorWords: this.getErrorWords().length,
        practiceHistory: this.getPracticeHistory().length,
        currentTextbook: !!Object.keys(this.getCurrentTextbook()).length,
        learningProgress: Object.keys(this.getLearningProgress()).length,
        dailyTasksData: Object.keys(this.getDailyTasksData()).length,
        unlockedAchievements: Object.keys(this.getUnlockedAchievements()).length
      };

      console.log('[SimpleStorage] 存储统计:', stats);
      return stats;
    } catch (error) {
      console.error('[SimpleStorage] 获取存储统计失败:', error);
      return {};
    }
  }

  // ========== 便捷方法 ==========

  // ========== 字词奖励管理 ==========

  /**
   * 获取字词奖励记录
   * @returns {Object} 字词奖励记录对象
   */
  static getWordRewards() {
    return this.get(this.KEYS.WORD_REWARDS, {});
  }

   
  

   

  /**
   * 清理旧的practiceData存储
   */
  static cleanupLegacyPracticeData() {
    try {
      wx.removeStorageSync('practiceData');
      console.log('[SimpleStorage] 已清理旧的practiceData存储');
    } catch (error) {
      console.error('[SimpleStorage] 清理practiceData失败:', error);
    }
  }

  /**
   * 清空所有数据
   */
  static clearAll() {
    Object.values(this.KEYS).forEach(key => {
      wx.removeStorageSync(key);
    });
    // 清空练习历史记录
    wx.removeStorageSync('practiceHistory');
    // 清理旧数据
    this.cleanupLegacyPracticeData();
  }

 
 

  // ========== 每日首次进入检查相关方法 ==========


  /**
   * 标记今天已进入小程序
   */
  static markEntryToday() {
    try {
      const today = new Date().toISOString().split('T')[0];
      const now = new Date().toISOString();

      const dailyEntry = {
        lastDate: today,
        lastTime: now,
        entryCount: (this.get(this.KEYS.DAILY_FIRST_ENTRY, {}).entryCount || 0) + 1
      };

      this.set(this.KEYS.DAILY_FIRST_ENTRY, dailyEntry);

      console.log('[SimpleStorage] 标记今日进入:', dailyEntry);
    } catch (error) {
      console.error('[SimpleStorage] 标记今日进入失败:', error);
    }
  }

  /**
   * 获取每日进入记录
   * @returns {object} 进入记录信息
   */
  static getDailyEntryInfo() {
    try {
      const dailyEntry = this.get(this.KEYS.DAILY_FIRST_ENTRY, {});
      const today = new Date().toISOString().split('T')[0];

      return {
        lastDate: dailyEntry.lastDate || null,
        lastTime: dailyEntry.lastTime || null,
        entryCount: dailyEntry.entryCount || 0,
        isFirstToday: dailyEntry.lastDate !== today
      };
    } catch (error) {
      console.error('[SimpleStorage] 获取每日进入信息失败:', error);
      return {
        lastDate: null,
        lastTime: null,
        entryCount: 0,
        isFirstToday: true
      };
    }
  }

   

  // ========== 首次挑战同步相关方法 ==========

  /**
   * 检查是否已进行过首次挑战同步
   * @returns {boolean} 是否已同步过
   */
  static hasFirstChallengeSynced() {
    try {
      const syncInfo = this.get(this.KEYS.FIRST_CHALLENGE_SYNC, {});
      return !!syncInfo.hasSynced;
    } catch (error) {
      console.error('[SimpleStorage] 检查首次挑战同步状态失败:', error);
      return false;
    }
  }

  /**
   * 标记首次挑战已同步
   */
  static markFirstChallengeSynced() {
    try {
      const syncInfo = {
        hasSynced: true,
        syncTime: new Date().toISOString(),
        syncDate: new Date().toISOString().split('T')[0]
      };

      this.set(this.KEYS.FIRST_CHALLENGE_SYNC, syncInfo);
      console.log('[SimpleStorage] 标记首次挑战已同步:', syncInfo);
    } catch (error) {
      console.error('[SimpleStorage] 标记首次挑战同步失败:', error);
    }
  }

  
 

  // **新增：迁移教材数据，处理游客升级到微信用户的情况**
  static migrateTextbookData(oldOpenId, newOpenId) {
    if (!oldOpenId || !newOpenId || oldOpenId === newOpenId) {
      console.log('[Migration] 无效的用户ID，跳过教材数据迁移。');
      return;
    }
    console.log(`[Migration] 开始迁移教材数据，从 ${oldOpenId} 到 ${newOpenId}`);
    
    try {
      const storageInfo = wx.getStorageInfoSync();
      // 查找旧游客的所有教材数据键
      const guestKeys = storageInfo.keys.filter(key => key.startsWith(`textbookData_${oldOpenId}_`));

      if (guestKeys.length === 0) {
        console.log('[Migration] 未找到需要迁移的游客教材数据。');
        return;
      }

      guestKeys.forEach(guestKey => {
        const guestData = wx.getStorageSync(guestKey);
        if (guestData) {
          // 创建新微信用户对应的键
          const newKey = guestKey.replace(`textbookData_${oldOpenId}_`, `textbookData_${newOpenId}_`);
          wx.setStorageSync(newKey, guestData);
          wx.removeStorageSync(guestKey); // 清理旧键
          console.log(`[Migration] ✅ 成功迁移 ${guestKey} -> ${newKey}`);
        }
      });

      console.log(`[Migration] 教材数据迁移完成，共迁移 ${guestKeys.length} 个教材的数据。`);
    } catch (e) {
      console.error('[Migration] 迁移教材数据时发生错误:', e);
    }
  }

 

 

  
 
 
 
 
 
 
 
 
}

module.exports = SimpleStorage; 