/**
 * 云函数调用统一管理器
 * 
 * 功能：
 * 1. 统一管理所有 wx.cloud.callFunction 调用
 * 2. 提供统一的错误处理、加载提示和日志记录
 * 3. 支持自定义配置每个调用的行为
 * 4. 按功能模块组织 API 调用
 * 5. 支持配置化选择云函数或 Node.js 服务端
 */

// 由于微信小程序环境对 new Function 受限，直接使用手写的 protobuf JsonWrapper 编解码，
// 避免依赖 protobufjs 动态生成函数。

// tag = 1 (field), wireType = 2 (length-delimited) => 0x0a
const WRAPPER_TAG = 0x0a;

// UTF-8 编解码器
const textEncoder = /** @type {TextEncoder} */ (typeof TextEncoder !== 'undefined' ? new TextEncoder() : null);
const textDecoder = /** @type {TextDecoder} */ (typeof TextDecoder !== 'undefined' ? new TextDecoder() : null);

/**
 * 编码 varint（无符号 32bit）
 * @param {number} value
 * @returns {Uint8Array}
 */
function encodeVarint(value) {
  const bytes = [];
  while (value > 127) {
    bytes.push((value & 0x7f) | 0x80);
    value >>>= 7;
  }
  bytes.push(value);
  return Uint8Array.from(bytes);
}

/**
 * 解码 varint，返回 [value, bytesCount]
 * @param {Uint8Array} buf
 * @param {number} offset
 * @returns {[number, number]}
 */
function decodeVarint(buf, offset = 0) {
  let value = 0;
  let shift = 0;
  let pos = offset;
  while (pos < buf.length) {
    const b = buf[pos++];
    value |= (b & 0x7f) << shift;
    if ((b & 0x80) === 0) break;
    shift += 7;
  }
  return [value, pos - offset];
}

/**
 * 将 JSON 字符串封装为 JsonWrapper 的 protobuf 二进制
 * @param {string} jsonStr
 * @returns {ArrayBuffer}
 */
function encodeJsonWrapper(jsonStr) {
  const utf8Bytes = textEncoder.encode(jsonStr);
  const lenBytes = encodeVarint(utf8Bytes.length);
  const buffer = new Uint8Array(1 + lenBytes.length + utf8Bytes.length);
  buffer[0] = WRAPPER_TAG;
  buffer.set(lenBytes, 1);
  buffer.set(utf8Bytes, 1 + lenBytes.length);
  return buffer.buffer;
}

/**
 * 解析 JsonWrapper 二进制，返回 JSON 字符串
 * @param {ArrayBuffer | Uint8Array} ab
 * @returns {string}
 */
function decodeJsonWrapper(ab) {
  const buf = ab instanceof Uint8Array ? ab : new Uint8Array(ab);
  if (buf[0] !== WRAPPER_TAG) {
    throw new Error('Invalid wrapper tag');
  }
  const [len, lenBytes] = decodeVarint(buf, 1);
  const strBytes = buf.subarray(1 + lenBytes, 1 + lenBytes + len);
  return textDecoder.decode(strBytes);
}

class CloudApi {
  constructor() {
    this.isLoading = false;
    this.loadingCount = 0;
    
    // 配置项
    this.config = {
      // 'cloud' = 使用云函数, 'server' = 使用 Node.js 服务端
      mode: wx.getStorageSync('api_mode') || 'server',
      
      // Node.js 服务端配置
      serverBaseUrl: 'https://educationapp.300066363.xyz/api', // 本地测试服务器
      remoteServerBaseUrl: 'https://educationapp.300066363.xyz/api', // 远程服务器
      
      // 请求超时时间
      timeout: 30000,
      
      // 是否启用日志
      enableLog: true,

      // 数据格式: 'json' 或 'protobuf'
      dataFormat: wx.getStorageSync('api_data_format') || 'json'
    };

    // 无需缓存 Type，使用手写编解码
  }

  /**
   * 设置 API 调用模式
   * @param {string} mode - 'cloud' 或 'server'
   * @param {string} serverUrl - 服务端 URL（可选）
   */
  setMode(mode, serverUrl) {
    if (!['cloud', 'server'].includes(mode)) {
      throw new Error('API 模式必须是 "cloud" 或 "server"');
    }
    
    this.config.mode = mode;
    
    if (serverUrl) {
      this.config.serverBaseUrl = serverUrl;
    }
    
    // 保存到本地存储
    wx.setStorageSync('api_mode', mode);
    if (serverUrl) {
      wx.setStorageSync('api_server_url', serverUrl);
    }
    
    console.log(`[CloudApi] API 模式已切换为: ${mode}${serverUrl ? ', 服务端: ' + serverUrl : ''}`);
  }

  /**
   * 获取当前 API 模式
   */
  getMode() {
    return this.config.mode;
  }

  /**
   * 设置数据格式（json / protobuf）
   * @param {'json'|'protobuf'} format
   */
  setDataFormat(format) {
    if (!['json', 'protobuf'].includes(format)) {
      throw new Error('数据格式必须是 "json" 或 "protobuf"');
    }
    this.config.dataFormat = format;
    wx.setStorageSync('api_data_format', format);
    console.log(`[CloudApi] 数据格式已切换为: ${format}`);
  }

  /**
   * 获取当前数据格式
   */
  getDataFormat() {
    return this.config.dataFormat;
  }

  /**
   * 获取 JsonWrapper 的 protobuf Type
   *
   * message JsonWrapper {
   *   string json = 1;
   * }
   */
  getProtoWrapperType() {
    if (!this._protoWrapperType) {
      const protoStr = `syntax = "proto3"; message JsonWrapper { string json = 1; }`;
      const root = protobuf.parse(protoStr).root;
      this._protoWrapperType = root.lookupType('JsonWrapper');
    }
    return this._protoWrapperType;
  }

  /**
   * 通用的 API 调用方法（支持云函数和服务端）
   * @param {string} name - 云函数名称或 API 路径
   * @param {object} data - 传递的数据
   * @param {object} options - 调用选项
   * @param {boolean} options.showLoading - 是否显示加载提示
   * @param {string} options.loadingText - 加载提示文字
   * @param {boolean} options.enableLog - 是否启用日志
   * @param {number} options.timeout - 超时时间（毫秒）
   * @param {string} options.method - HTTP 方法（仅服务端模式）
   * @param {string} options.endpoint - 具体的 API 端点（仅服务端模式）
   * @returns {Promise} API 调用结果
   */
  async call(name, data = {}, options = {}) {
    const {
      showLoading = false,
      loadingText = '请稍候...',
      enableLog = this.config.enableLog,
      timeout = this.config.timeout,
      method = 'POST',
      endpoint = ''
    } = options;

    const callId = this.generateCallId();
    
    if (enableLog) {
      console.log(`[CloudApi:${callId}] [${this.config.mode.toUpperCase()}] 调用: ${name}`, data);
    }

    // 显示加载提示
    if (showLoading) {
      this.showLoading(loadingText);
    }

    try {
      let result;

      if (this.config.mode === 'cloud') {
        // 云函数模式
        result = await this.callCloudFunction(name, data, timeout);
      } else {
        // 服务端模式
        result = await this.callServer(name, data, { method, endpoint, timeout });
      }

      if (enableLog) {
        console.log(`[CloudApi:${callId}] [${this.config.mode.toUpperCase()}] 返回:`, result);
      }

      return result;

    } catch (error) {
      if (enableLog) {
        console.error(`[CloudApi:${callId}] [${this.config.mode.toUpperCase()}] 调用失败:`, error);
      }
      throw error;
    } finally {
      // 隐藏加载提示
      if (showLoading) {
        this.hideLoading();
      }
    }
  }

  /**
   * 云函数调用
   */
  async callCloudFunction(name, data, timeout) {
    // 创建超时Promise
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error(`云函数调用超时: ${name}`)), timeout);
    });

    // 实际的云函数调用
    const callPromise = wx.cloud.callFunction({
      name,
      data
    });

    // 使用Promise.race处理超时
    const result = await Promise.race([callPromise, timeoutPromise]);

    // 检查返回结果格式
    if (!result || typeof result !== 'object') {
      throw new Error(`云函数返回格式异常: ${name}`);
    }

    return result;
  }

  /**
   * 服务端 API 调用
   */
  async callServer(name, data, { method, endpoint, timeout }) {
    // 构建请求 URL
    let url = endpoint || `${this.config.serverBaseUrl}/${name}`;
    
    // 获取用户信息用于身份验证
    const userData = wx.getStorageSync('userData') || {};
    const userOpenid = userData.openid || userData._id;

    console.log(`[CloudApi] 发送请求: ${method.toUpperCase()} ${url}`, {
      hasUserData: !!userData,
      userOpenid: userOpenid,
      openid: userData.openid,
      _id: userData._id
    }); // 添加调试日志

    // 创建超时Promise
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error(`服务端调用超时: ${url}`)), timeout);
    });

    // 根据数据格式决定编码/解码方式
    const useProtobuf = this.config.dataFormat === 'protobuf';
    console.log('useProtobuf',useProtobuf)
    // 如果使用 protobuf，编码请求体
    let requestData = data;
    let contentTypeHeader = 'application/json';
    let responseType = useProtobuf ? 'arraybuffer' : 'text';

    if (useProtobuf && method.toUpperCase() !== 'GET') {
      try {
        requestData = encodeJsonWrapper(JSON.stringify(data));
        contentTypeHeader = 'application/x-protobuf';
      } catch (e) {
        console.error('[CloudApi] Protobuf 编码失败:', e);
        throw e;
      }
    }

    // 实际的 HTTP 请求
    const requestPromise = new Promise((resolve, reject) => {
      const requestConfig = {
        url,
        method,
        responseType,
        header: {
          'Content-Type': contentTypeHeader,
          'Accept': useProtobuf ? 'application/x-protobuf' : 'application/json',
          'x-openid': userOpenid // 传递用户标识
        },
      };

      // GET 请求不应该有 body 数据
      if (method.toUpperCase() !== 'GET') {
        requestConfig.data = requestData;
      } else {
        // 对于 GET 请求，将参数添加到 URL 中
        if (data && Object.keys(data).length > 0) {
          const queryParams = [];
          Object.keys(data).forEach(key => {
            if (data[key] !== undefined && data[key] !== null) {
              queryParams.push(`${encodeURIComponent(key)}=${encodeURIComponent(data[key])}`);
            }
          });
          const queryString = queryParams.join('&');
          if (queryString) {
            url = `${url}?${queryString}`;
            requestConfig.url = url;
          }
        }
      }

      wx.request({
        ...requestConfig,
        success: (res) => {
          if (res.statusCode === 200) {
            try {
              let responseData = res.data;
              // 如果响应为 protobuf，需要解码
              const resContentType = (res.header['Content-Type'] || res.header['content-type'] || '').toLowerCase();
              const isProtoRes = useProtobuf && resContentType.includes('application/x-protobuf');
              if (isProtoRes) {
                const jsonStr = decodeJsonWrapper(new Uint8Array(res.data));
                responseData = JSON.parse(jsonStr);
              }

              resolve({
                result: responseData
              });
            } catch (e) {
              reject(new Error(`响应解析失败: ${e.message}`));
            }
          } else {
            reject(new Error(`HTTP ${res.statusCode}: ${res.data?.message || '请求失败'}`));
          }
        },
        fail: (err) => {
          reject(new Error(`网络请求失败: ${err.errMsg || '未知错误'}`));
        }
      });
    });

    // 使用Promise.race处理超时
    const result = await Promise.race([requestPromise, timeoutPromise]);

    return result;
  }

  /**
   * 显示加载提示
   */
  showLoading(title = '请稍候...') {
    this.loadingCount++;
    if (this.loadingCount === 1) {
      wx.showLoading({ title, mask: true });
      this.isLoading = true;
    }
  }

  /**
   * 隐藏加载提示
   */
  hideLoading() {
    this.loadingCount--;
    if (this.loadingCount <= 0) {
      this.loadingCount = 0;
      if (this.isLoading) {
        wx.hideLoading();
        this.isLoading = false;
      }
    }
  }

  /**
   * 生成调用ID（用于日志追踪）
   */
  generateCallId() {
    return Math.random().toString(36).substr(2, 8);
  }

  // =====================
  // 数据同步相关 API
  // =====================
  sync = {
    /**
     * 检查同步策略
     */
    check: async (localSummary, options = {}) => {
      if (this.config.mode === 'cloud') {
        return await this.call('syncUserDataOnLogin', {
          operation: 'check',
          localSummary
        }, {
          showLoading: options.showLoading !== false,
          loadingText: '检查同步策略...',
          ...options
        });
      } else {
        return await this.call('sync/check', {
          localSummary
        }, {
          showLoading: options.showLoading !== false,
          loadingText: '检查同步策略...',
          ...options
        });
      }
    },

    /**
     * 上传本地数据到云端
     */
    upload: async (localData, options = {}) => {
      if (this.config.mode === 'cloud') {
        return await this.call('syncUserDataOnLogin', {
          operation: 'upload',
          localData
        }, {
          showLoading: options.showLoading !== false,
          loadingText: '上传数据中...',
          ...options
        });
      } else {
        return await this.call('sync/upload', {
          localData
        }, {
          showLoading: options.showLoading !== false,
          loadingText: '上传数据中...',
          ...options
        });
      }
    },

    /**
     * 从云端下载数据
     */
    download: async (options = {}) => {
      if (this.config.mode === 'cloud') {
        return await this.call('syncUserDataOnLogin', {
          operation: 'download'
        }, {
          showLoading: options.showLoading !== false,
          loadingText: '下载数据中...',
          ...options
        });
      } else {
        return await this.call('sync/download', {}, {
          showLoading: options.showLoading !== false,
          loadingText: '下载数据中...',
          ...options
        });
      }
    }
  };

  // =====================
  // 用户管理相关 API
  // =====================
  user = {
    /**
     * 使用微信 code 进行登录或注册
     * @param {string} code - wx.login() 获取的 code
     */
    wxLogin: async (code, options = {}) => {
      if (this.config.mode === 'cloud') {
        // 云函数模式：直接调用 userManager，无需传递 code（云函数自动获取 openid）
        return await this.call('userManager', {
          action: 'register' // 登录不存在时自动注册
        }, {
          showLoading: options.showLoading,
          loadingText: '登录中...',
          ...options
        });
      } else {
        // 服务端模式：使用 code 换取 openid
        return await this.call('user/wx-login', { code }, {
          showLoading: options.showLoading,
          loadingText: '登录中...',
          ...options
        });
      }
    },

    /**
     * 注册用户（旧版，已不推荐）
     * @param {object} userData - 用户信息
     * @param {string} userOpenid - 用户 openid
     */
    login: async (userOpenid, options = {}) => {
      return await this.call('user/login', { userOpenid }, options);
    },

    /**
     * 更新用户资料
     */
    update: async (userData, options = {}) => {
      if (this.config.mode === 'cloud') {
        return await this.call('userManager', {
          action: 'update',
          data: userData
        }, {
          showLoading: options.showLoading !== false,
          loadingText: '更新用户资料中...',
          ...options
        });
      } else {
        return await this.call('user/update', {
          data: userData
        }, {
          showLoading: options.showLoading !== false,
          loadingText: '更新用户资料中...',
          ...options
        });
      }
    },

    /**
     * 绑定用户 - 授权对方查看我的数据
     * @param {string} targetId - 目标用户的openid
     */
    bind: async (targetId, options = {}) => {
      if (this.config.mode === 'cloud') {
        return await this.call('userManager', {
          action: 'bind',
          data: { targetId }
        }, {
          showLoading: options.showLoading !== false,
          loadingText: '绑定中...',
          ...options
        });
      } else {
        return await this.call('user/bind', {
          targetId
        }, {
          showLoading: options.showLoading !== false,
          loadingText: '绑定中...',
          ...options
        });
      }
    },

    /**
     * 解绑用户 - 取消对某人的授权
     * @param {string} watcherId - 观看者的openid
     */
    unbind: async (watcherId, options = {}) => {
      if (this.config.mode === 'cloud') {
        return await this.call('userManager', {
          action: 'unbind',
          data: { watcherId }
        }, {
          showLoading: options.showLoading !== false,
          loadingText: '解绑中...',
          ...options
        });
      } else {
        return await this.call('user/unbind', {
          watcherId
        }, {
          showLoading: options.showLoading !== false,
          loadingText: '解绑中...',
          ...options
        });
      }
    },

    /**
     * 获取绑定列表
     */
    getBindList: async (options = {}) => {
      if (this.config.mode === 'cloud') {
        return await this.call('userManager', {
          action: 'getBindList'
        }, {
          showLoading: options.showLoading,
          ...options
        });
      } else {
        return await this.call('user/bind/list', {}, {
          method: 'GET',
          showLoading: options.showLoading,
          ...options
        });
      }
    },

    /**
     * 获取学习进度数据
     * @param {string} targetId - 目标用户的openid（可选，默认为自己）
     */
    getProgress: async (targetId, options = {}) => {
      if (this.config.mode === 'cloud') {
        // 云函数模式
        return await this.call('userManager', {
          action: 'getProgress',
          data: targetId ? { targetId } : {}
        }, {
          showLoading: options.showLoading,
          ...options
        });
      } else {
        const params = targetId ? { targetId } : {};
        return await this.call('user/progress', params, {
          method: 'GET',
          showLoading: options.showLoading,
          ...options
        });
      }
    },

    /**
     * 获取练习历史记录
     * @param {Object} options - 查询选项
     * @param {string} options.courseId - 课程ID（可选）
     * @param {string} options.type - 练习类型（可选）
     * @param {string} options.startDate - 开始日期（可选）
     * @param {string} options.endDate - 结束日期（可选）
     * @param {number} options.limit - 限制数量（可选，默认50）
     * @param {number} options.offset - 偏移量（可选，默认0）
     */
    getPracticeHistory: async (options = {}) => {
      if (this.config.mode === 'cloud') {
        // 云函数模式 - 暂时不支持
        return {
          success: false,
          error: '云函数模式暂不支持练习历史查询'
        };
      } else {
        // 手动构建查询字符串（微信小程序不支持URLSearchParams）
        const params = [];
        if (options.courseId) params.push(`courseId=${encodeURIComponent(options.courseId)}`);
        if (options.type) params.push(`type=${encodeURIComponent(options.type)}`);
        if (options.startDate) params.push(`startDate=${encodeURIComponent(options.startDate)}`);
        if (options.endDate) params.push(`endDate=${encodeURIComponent(options.endDate)}`);
        if (options.limit) params.push(`limit=${encodeURIComponent(options.limit)}`);
        if (options.offset) params.push(`offset=${encodeURIComponent(options.offset)}`);

        const queryString = params.length > 0 ? `?${params.join('&')}` : '';

        return await this.call(`user/practice-history${queryString}`, {}, {
          method: 'GET',
          showLoading: options.showLoading
        });
      }
    },

    /**
     * 获取指定用户错题数据
     * @param {string} userId - 目标用户 openid
     * @param {object} options - 其他选项
     */
    getErrorWords: async (userId, options = {}) => {
      if (this.config.mode === 'cloud') {
        // 云函数模式 - 需后端支持
        return await this.call('userManager', {
          action: 'getErrorWords',
          data: { userId }
        }, {
          showLoading: options.showLoading,
          ...options
        });
      } else {
        // 服务端模式
        return await this.call('user/error-words', { userId }, {
          method: 'GET',
          showLoading: options.showLoading,
          ...options
        });
      }
    }
  };

  // =====================
  // VIP会员相关 API
  // =====================
  vip = {
    /**
     * 获取VIP状态
     */
    getStatus: async (options = {}) => {
      return await this.call('vip/status', {}, {
        showLoading: options.showLoading,
        method: 'GET',
        ...options
      });
    },

    /**
     * 创建VIP支付订单
     */
    createOrder: async (vipType, options = {}) => {
      return await this.call('vip/create-order', {
        vipType
      }, {
        showLoading: options.showLoading !== false,
        loadingText: '正在创建订单...',
        method: 'POST',
        ...options
      });
    },

    /**
     * 查询订单状态
     */
    queryOrderStatus: async (orderId, options = {}) => {
      return await this.call(`vip/order-status/${orderId}`, {}, {
        showLoading: options.showLoading,
        method: 'GET',
        ...options
      });
    },

    /**
     * 验证VIP特权
     */
    checkPrivilege: async (privilegeKey, options = {}) => {
      return await this.call('vip/check-privilege', {
        privilegeKey
      }, {
        showLoading: false,
        method: 'POST',
        ...options
      });
    },

    /**
     * 获取VIP特权列表
     */
    getPrivileges: async (options = {}) => {
      return await this.call('vip/privileges', {}, {
        showLoading: false,
        method: 'GET',
        ...options
      });
    },

    /**
     * 取消VIP
     */
    cancel: async (reason, options = {}) => {
      return await this.call('vip/cancel', {
        reason
      }, {
        showLoading: options.showLoading !== false,
        loadingText: '正在取消VIP...',
        method: 'POST',
        ...options
      });
    },

    /**
     * 测试支付成功（开发测试用）
     */
    testPaymentSuccess: async (orderId, vipType, options = {}) => {
      return await this.call('vip/test-payment-success', {
        orderId,
        vipType
      }, {
        showLoading: options.showLoading !== false,
        loadingText: '正在处理支付...',
        method: 'POST',
        ...options
      });
    }
  };

  // =====================
  // OCR 识别相关 API
  // =====================
  ocr = {
    /**
     * 手写文字识别
     */
    handwriting: async (imageBase64, options = {}) => {
      // 处理base64数据，移除前缀
      let base64Data = imageBase64;
      if (base64Data.indexOf('data:image') !== -1) {
        base64Data = base64Data.split(',')[1];
      }

      if (this.config.mode === 'cloud') {
        return await this.call('ocrHandwriting', {
          image: base64Data,
          language: 'cn|en' // 中英文混合识别
        }, {
          showLoading: options.showLoading !== false,
          loadingText: '识别中...',
          timeout: 60000, // OCR识别需要更长时间
          ...options
        });
      } else {
        return await this.call('ocr/handwriting', {
          image: base64Data,
          language: 'cn|en' // 中英文混合识别
        }, {
          showLoading: options.showLoading !== false,
          loadingText: '识别中...',
          timeout: 60000, // OCR识别需要更长时间
          ...options
        });
      }
    },

    /**
     * 通用文字识别
     */
    general: async (imageBase64, options = {}) => {
      // 去掉 data:image/...;base64, 前缀
      let base64Data = imageBase64;
      if (base64Data.indexOf('data:image') !== -1) {
        base64Data = base64Data.split(',')[1];
      }

      if (this.config.mode === 'cloud') {
        return await this.call('ocrUniversal', {
          image: base64Data,
          language: 'cn|en'
        }, {
          showLoading: options.showLoading !== false,
          loadingText: '识别中...',
          timeout: 60000,
          ...options
        });
      } else {
        return await this.call('ocr/general', {
          image: base64Data,
          language: 'cn|en'
        }, {
          showLoading: options.showLoading !== false,
          loadingText: '识别中...',
          timeout: 60000,
          ...options
        });
      }
    },

    /**
     * 阿里云百炼 OCR 文字识别
     */
    aliyun: async (imageBase64, options = {}) => {
      // 去掉 data:image/...;base64, 前缀
      let base64Data = imageBase64;
      if (base64Data.indexOf('data:image') !== -1) {
        base64Data = base64Data.split(',')[1];
      }

      if (this.config.mode === 'cloud') {
        // 若后续实现云函数，可替换名称
        return await this.call('aliyunOcr', {
          image: base64Data
        }, {
          showLoading: options.showLoading !== false,
          loadingText: '识别中...',
          timeout: 60000,
          ...options
        });
      } else {
        return await this.call('ocr/aliyunocr', {
          image: base64Data
        }, {
          showLoading: options.showLoading !== false,
          loadingText: '识别中...',
          timeout: 60000,
          ...options
        });
      }
    }
  };

  // =====================
  // 系统测试相关 API
  // =====================
  
  /**
   * 测试API连接
   * @param {object} options - 测试选项
   * @returns {Promise<boolean>} 连接是否成功
   */
  async testConnection(options = {}) {
    try {
      if (this.config.mode === 'cloud') {
        // 测试云函数连接
        const result = await this.call('testSync', {
          test: true
        }, {
          showLoading: false,
          enableLog: false,
          timeout: 10000,
          ...options
        });
        
        return result && result.result && result.result.success;
      } else {
        // 测试服务端连接 - 使用健康检查接口
        const result = await this.call('health', {}, {
          showLoading: false,
          enableLog: false,
          timeout: 10000,
          method: 'GET',
          endpoint: `${this.config.serverBaseUrl.replace('/api', '')}/health`,
          ...options
        });
        
        return result && result.result && result.result.status === 'ok';
      }
    } catch (error) {
      console.error('[CloudApi] 连接测试失败:', error);
      return false;
    }
  }
}

// 创建单例实例
const cloudApi = new CloudApi();

// 导出模块
module.exports = cloudApi; 