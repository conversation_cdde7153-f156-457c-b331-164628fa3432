# 趋势图问题快速修复指南

## 问题现象：趋势图无数据显示

### 快速诊断步骤

#### 1. 检查API连接（最常见问题）

在学习进度页面的控制台中运行：

```javascript
// 快速检查API连接
const TrendChartDebug = require('./utils/trendChartDebug');
await TrendChartDebug.debugApiConnection();
```

**期望结果**：
- ✅ API调用成功，返回数据
- ❌ API调用失败，显示错误信息

#### 2. 检查数据库中是否有练习记录

```javascript
// 检查练习历史API
const cloudApi = require('./utils/cloudApi');
const response = await cloudApi.user.getPracticeHistory({ limit: 10 });
console.log('API响应:', response);
```

**期望结果**：
- 如果`response.data`为空数组，说明数据库中没有练习记录
- 如果有数据，检查数据格式是否正确

#### 3. 检查本地数据（回退方案）

```javascript
// 检查本地存储
const SimpleStorage = require('./utils/simpleStorage');
const localHistory = SimpleStorage.getPracticeHistory();
console.log('本地练习历史:', localHistory.length, '条');
```

### 常见问题及解决方案

#### 问题1：API返回空数据
**原因**：数据库中没有练习记录
**解决方案**：
1. 进行一些练习活动，生成练习记录
2. 或者使用测试数据进行调试

```javascript
// 生成测试数据
const TrendChartDebug = require('./utils/trendChartDebug');
await TrendChartDebug.generateTestDataAndVerify();
```

#### 问题2：API调用失败
**原因**：网络问题、服务器问题或认证问题
**解决方案**：
1. 检查网络连接
2. 确认服务器运行状态
3. 检查用户登录状态

```javascript
// 检查API配置
const cloudApi = require('./utils/cloudApi');
console.log('API配置:', cloudApi.config);
```

#### 问题3：数据格式错误
**原因**：API返回的数据格式与预期不符
**解决方案**：
1. 检查API返回的数据结构
2. 确认数据转换逻辑

```javascript
// 检查数据转换
const response = await cloudApi.user.getPracticeHistory({ limit: 1 });
if (response.data && response.data.length > 0) {
  const rawRecord = response.data[0];
  const convertedRecord = TrendChartData.convertApiRecord(rawRecord);
  console.log('原始数据:', rawRecord);
  console.log('转换后数据:', convertedRecord);
}
```

#### 问题4：语法错误
**原因**：JavaScript语法错误导致模块加载失败
**解决方案**：
1. 检查控制台是否有语法错误提示
2. 确认类方法之间没有多余的逗号
3. 验证所有括号和大括号匹配

```javascript
// 测试模块加载
try {
  const TrendChartData = require('./utils/trendChartData');
  console.log('✅ 模块加载成功');
} catch (error) {
  console.error('❌ 语法错误:', error.message);
}
```

#### 问题5：Canvas初始化失败
**原因**：页面渲染时机问题
**解决方案**：
1. 确保在`onReady`中初始化Canvas
2. 检查Canvas元素是否存在

```javascript
// 检查Canvas状态
const query = wx.createSelectorQuery();
query.select('#trendChart').fields({ node: true, size: true }).exec((res) => {
  console.log('Canvas查询结果:', res);
});
```

### 快速修复脚本

#### 完整诊断脚本
```javascript
// 在学习进度页面控制台中运行
async function quickDiagnosis() {
  console.log('🔍 开始快速诊断...');
  
  // 1. 检查API
  const TrendChartDebug = require('./utils/trendChartDebug');
  const apiOk = await TrendChartDebug.debugApiConnection();
  
  // 2. 检查本地数据
  const localData = TrendChartDebug.debugLocalData();
  
  // 3. 生成趋势图数据
  const trendData = await TrendChartDebug.debugTrendDataGeneration();
  
  console.log('📋 诊断结果:');
  console.log('- API连接:', apiOk ? '✅' : '❌');
  console.log('- 本地数据:', localData ? '✅' : '❌');
  console.log('- 趋势图生成:', trendData ? '✅' : '❌');
  
  if (!apiOk && (!localData || localData.practiceHistory === 0)) {
    console.log('💡 建议: 生成测试数据进行调试');
    await TrendChartDebug.generateTestDataAndVerify();
  }
}

await quickDiagnosis();
```

#### 强制刷新趋势图
```javascript
// 强制重新加载趋势图数据
async function refreshTrendChart() {
  const page = getCurrentPages()[getCurrentPages().length - 1];
  if (page && page.loadTrendChartData) {
    console.log('🔄 正在刷新趋势图...');
    await page.loadTrendChartData();
    console.log('✅ 趋势图刷新完成');
  } else {
    console.log('❌ 当前页面不支持趋势图刷新');
  }
}

await refreshTrendChart();
```

### 开发环境调试

#### 启用详细日志
在`trendChartData.js`中临时添加更多日志：

```javascript
// 在getPracticeHistory方法开头添加
console.log('[DEBUG] 开始获取练习历史记录');
console.log('[DEBUG] API配置:', cloudApi.config);
console.log('[DEBUG] 目标用户ID:', targetUserId);
```

#### 模拟API响应
如果API不可用，可以模拟响应进行调试：

```javascript
// 在trendChartData.js中临时添加
static async getPracticeHistory(targetUserId) {
  // 模拟API响应
  const mockData = [
    {
      timestamp: '2025-01-15T10:00:00Z',
      accuracy: 85,
      correct_count: 8,
      total_count: 10,
      practice_time: 300,
      type: 'normal_practice',
      course_id: 'test_course'
    }
    // ... 更多模拟数据
  ];
  
  console.log('[MOCK] 使用模拟数据');
  return mockData.map(record => this.convertApiRecord(record));
}
```

### 生产环境问题排查

#### 1. 检查用户反馈
- 收集用户设备信息
- 确认问题复现步骤
- 检查是否为特定用户问题

#### 2. 服务器日志检查
- 查看API调用日志
- 检查数据库连接状态
- 确认练习记录同步状态

#### 3. 数据一致性检查
```sql
-- 检查数据库中的练习记录
SELECT COUNT(*) as total_records, 
       MAX(timestamp) as latest_record,
       MIN(timestamp) as earliest_record
FROM practice_history 
WHERE user_id = ?;
```

### 预防措施

#### 1. 数据备份
- 定期备份本地练习数据
- 确保数据同步机制正常工作

#### 2. 错误监控
- 添加API调用失败的监控
- 记录Canvas初始化失败的情况

#### 3. 用户提示
- 在无数据时显示友好提示
- 提供重试机制

### 联系支持

如果以上方法都无法解决问题，请提供以下信息：

1. **设备信息**：微信版本、手机型号、操作系统
2. **错误日志**：控制台错误信息
3. **诊断结果**：运行快速诊断脚本的输出
4. **复现步骤**：详细的问题复现步骤

---

**最后更新**：2025年1月
**适用版本**：趋势图功能 v1.0+
