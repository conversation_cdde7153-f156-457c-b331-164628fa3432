/**
 * 语法测试文件
 * 用于验证修复后的代码是否能正常加载
 */

try {
  // 测试TrendChartData模块加载
  const TrendChartData = require('./trendChartData');
  console.log('✅ TrendChartData模块加载成功');
  
  // 测试TrendChart模块加载
  const TrendChart = require('./trendChart');
  console.log('✅ TrendChart模块加载成功');
  
  // 测试TrendChartTest模块加载
  const TrendChartTest = require('./trendChartTest');
  console.log('✅ TrendChartTest模块加载成功');
  
  // 测试TrendChartDebug模块加载
  const TrendChartDebug = require('./trendChartDebug');
  console.log('✅ TrendChartDebug模块加载成功');
  
  console.log('🎉 所有趋势图相关模块加载成功！');
  
} catch (error) {
  console.error('❌ 模块加载失败:', error);
  console.error('错误详情:', error.message);
  console.error('错误堆栈:', error.stack);
}

module.exports = {
  testModuleLoading: () => {
    console.log('开始测试模块加载...');
    // 测试代码在上面的try-catch块中
  }
};
