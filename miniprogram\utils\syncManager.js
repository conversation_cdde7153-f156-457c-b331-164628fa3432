/**
 * 云同步管理器 - 重构版
 * 简洁的云同步逻辑，适用于全新项目
 */

const SimpleStorage = require('./simpleStorage');
const cloudApi = require('./cloudApi');

class SyncManager {
  // 同步动作常量
  static SYNC_ACTIONS = {
    NO_ACTION: 'no_action',      // 无需同步
    UPLOAD: 'upload',            // 上传本地数据到云端
    DOWNLOAD: 'download',        // 下载云端数据到本地
    CONFLICT: 'conflict'         // 数据冲突，需要用户选择
  };

  // 同步状态标记
  static _syncInProgress = false;

  /**
   * 检查本地数据状态摘要
   */
  static checkLocalDataStatus(enableDetailedLogging = false) {
    try {
      const userData = SimpleStorage.getUserData() || {};
      const textbookWordRewards = this.collectAllTextbookWordRewards();
      const wordRewardsCount = Object.values(textbookWordRewards)
        .reduce((sum, obj) => sum + Object.keys(obj).length, 0);
      const learningProgress = SimpleStorage.getLearningProgress() || {};
      const errorWords = SimpleStorage.getErrorWords() || [];
      const practiceHistory = SimpleStorage.getPracticeHistory() || [];
      const dailyTasksData = SimpleStorage.getDailyTasksData() || {};

      // 判断是否有重要数据
      const userDataHasStats = (
        (userData.totalPracticeCount || userData.totalPracticeTime || userData.totalCorrectCount || userData.totalWrongCount) > 0
      );

      const hasSignificantData = (
        userDataHasStats ||
        practiceHistory.length > 0 ||
        wordRewardsCount > 0 ||
        Object.keys(learningProgress).length > 0 ||
        errorWords.length > 0
      );

      // 获取最新的修改时间（只在需要详细日志时启用）
      const lastModified = this.getLatestModifiedTime(userData, enableDetailedLogging);

      // 若本地无重要数据，则把版本号降为 0，强制与云端比较时触发 download
      const effectiveVersion = hasSignificantData ? (userData.dataVersion || 1) : 0;

      const summary = {
        userOpenid: userData.openid,
        hasUserData: !!userData.openid,
        hasSignificantData,
        dataVersion: effectiveVersion,
        lastModified: lastModified,
        dataTypeCounts: {
          practiceHistory: practiceHistory.length,
          wordRewards: wordRewardsCount,
          learningProgress: Object.keys(learningProgress).length,
          errorWords: errorWords.length,
          dailyTasksDays: Object.keys(dailyTasksData).length
        }
      };

      if (enableDetailedLogging) {
        console.log('[SyncManager] 本地数据状态:', summary);
      }
      return summary;
    } catch (error) {
      console.error('[SyncManager] 检查本地数据状态失败:', error);
      return { hasUserData: false, hasSignificantData: false };
    }
  }

  /**
   * 获取最新的数据修改时间
   */
  static getLatestModifiedTime(userData, shouldLog = false) {
    console.log('[SyncManager] 获取最新的数据修改时间', userData);
    try {
      const modifiedTimes = [];

      // 用户数据的修改时间
      if (userData.updateTime) {
        modifiedTimes.push(new Date(userData.updateTime));
      }
      if (userData.lastModified) {
        modifiedTimes.push(new Date(userData.lastModified));
      }

      // 各个数据类型的修改时间
      const dataTypes = ['userData', 'wordRewards', 'learningProgress', 'errorWords', 'dailyTasksData', 'currentTextbook'];

      for (const dataType of dataTypes) {
        const lastModified = SimpleStorage.get(`${dataType}_lastModified`, null);
        if (lastModified) {
          modifiedTimes.push(new Date(lastModified));
        }
      }

      // 如果没有任何修改时间，使用当前时间
      if (modifiedTimes.length === 0) {
        const now = new Date().toISOString();
        //   if (shouldLog) {
        console.log('[SyncManager] 未找到修改时间，使用当前时间:', now);
        //   }
        return now;
      }

      // 返回最新的时间
      const latestTime = new Date(Math.max(...modifiedTimes));
      const latestTimeISO = latestTime.toISOString();

      // 只在明确需要详细日志时才打印
      // if (shouldLog) {
      console.log('[SyncManager] 计算最新修改时间:', {
        找到的时间数: modifiedTimes.length,
        最新时间: latestTimeISO
      });
      // }

      return latestTimeISO;
    } catch (error) {
      console.error('[SyncManager] 获取修改时间失败:', error);
      return new Date().toISOString();
    }
  }

  /**
   * 收集所有教材的字词奖励数据
   */
  static collectAllTextbookWordRewards() {
    try {
      const allWordRewards = {};
      
      // 获取所有以 wordRewards_ 开头的存储键
      const storageInfo = wx.getStorageInfoSync();
      console.log('[SyncManager] 🔍 检查本地存储键:', storageInfo.keys);
      
      const wordRewardKeys = storageInfo.keys.filter(key => 
        key.startsWith('wordRewards_') && !key.endsWith('_lastModified')
      );
      
      console.log('[SyncManager] 🎯 发现教材字词奖励键:', wordRewardKeys);
      
      // 收集每个教材的字词奖励数据
      wordRewardKeys.forEach(key => {
        try {
          const rewards = wx.getStorageSync(key) || {};
          console.log(`[SyncManager] 🔍 检查存储键 ${key}:`, {
            数据存在: !!rewards,
            数据类型: typeof rewards,
            字词数量: Object.keys(rewards).length,
            示例字词: Object.keys(rewards).slice(0, 3)
          });
          
          if (Object.keys(rewards).length > 0) {
            allWordRewards[key] = rewards;
            console.log(`[SyncManager] ✅ 收集教材奖励 ${key}:`, Object.keys(rewards).length, '个字词');
          } else {
            console.log(`[SyncManager] ⚠️  跳过空奖励数据 ${key}`);
          }
        } catch (error) {
          console.error(`[SyncManager] ❌ 收集教材奖励失败 ${key}:`, error);
        }
      });
      
      console.log('[SyncManager] 📊 收集结果汇总:', {
        发现的教材数量: wordRewardKeys.length,
        有数据的教材数量: Object.keys(allWordRewards).length,
        教材列表: Object.keys(allWordRewards),
        总字词数量: Object.values(allWordRewards).reduce((sum, rewards) => sum + Object.keys(rewards).length, 0)
      });
      
      return allWordRewards;
    } catch (error) {
      console.error('[SyncManager] 收集所有教材字词奖励失败:', error);
      return {};
    }
  }

  /**
   * 收集本地数据用于上传（增量收集）
   */
  static collectLocalData() {
    try {
      // 获取上次同步时间
      const lastSyncTime = SimpleStorage.getLastSyncTime() || 0;
      const now = new Date().toISOString();
      const nowTimestamp = Date.now();

      // 收集所有本地数据
      const userData = SimpleStorage.getUserData() || {};
      const textbookData = SimpleStorage.getTextbookData() || {};
      const textbookWordRewards = this.collectAllTextbookWordRewards();
      const learningProgress = SimpleStorage.getLearningProgress() || {};
      const errorWords = SimpleStorage.getErrorWords() || [];
      const currentTextbook = SimpleStorage.getCurrentTextbook() || {};
      const practiceHistoryBase = SimpleStorage.getPracticeHistoryBase() || [];
      const dailyTasksData = SimpleStorage.getDailyTasksData() || {};

      // 筛选需要同步的数据
      const changedData = this.filterChangedData({
        userData,
        textbookData,
        textbookWordRewards,
        learningProgress,
        errorWords,
        currentTextbook,
        practiceHistoryBase,
        dailyTasksData
      }, lastSyncTime);

      // 如果没有变化的数据，返回null
      if (!changedData || Object.keys(changedData).length === 0) {
        console.log('[SyncManager] 没有需要同步的数据变化');
        return null;
      }

      const limitedDailyTasksData = SimpleStorage.limitDailyTasksData(dailyTasksData);
      if (limitedDailyTasksData !== dailyTasksData) {
        SimpleStorage.setDailyTasksData(limitedDailyTasksData);
      }

      // 更新版本号和修改时间
      if (changedData.userData) {
        changedData.userData.dataVersion = (userData.dataVersion || 1) + 1;
        changedData.userData.lastModified = now;
      }

      // 构建最终的上传数据
      const localData = {
        ...changedData,
        uploadTime: now,
        isIncremental: true // 标记为增量同步
      };

      console.log('[SyncManager] 收集变化数据完成:', {
        changedDataTypes: Object.keys(changedData),
        practiceHistoryCount: changedData.practiceHistory?.length || 0,
        errorWordsCount: changedData.errorWords?.length || 0,
        lastSyncTime: new Date(lastSyncTime).toISOString()
      });

      return localData;
    } catch (error) {
      console.error('[SyncManager] 收集本地数据失败:', error);
      return null;
    }
  }

  /**
   * 筛选有变化的数据
   * @param {Object} allData - 所有本地数据
   * @param {number} lastSyncTime - 上次同步时间戳
   * @returns {Object} 有变化的数据
   */
  static filterChangedData(allData, lastSyncTime) {
    const changedData = {};

    try {
      // 检查用户数据是否有变化
      if (this.hasDataChanged(allData.userData, lastSyncTime, 'userData')) {
        changedData.userData = allData.userData;
      }

      // 检查教材数据是否有变化
      if (this.hasDataChanged(allData.textbookData, lastSyncTime, 'textbookData')) {
        changedData.textbookData = allData.textbookData;
      }

      // 检查字词奖励数据是否有变化
      if (this.hasDataChanged(allData.textbookWordRewards, lastSyncTime, 'textbookWordRewards')) {
        changedData.textbookWordRewards = allData.textbookWordRewards;
      }

      // 检查学习进度是否有变化
      if (this.hasDataChanged(allData.learningProgress, lastSyncTime, 'learningProgress')) {
        changedData.learningProgress = allData.learningProgress;
      }

      // 检查当前教材是否有变化
      if (this.hasDataChanged(allData.currentTextbook, lastSyncTime, 'currentTextbook')) {
        changedData.currentTextbook = allData.currentTextbook;
      }

      // 检查错题数据是否有变化
      if (this.hasDataChanged(allData.errorWords, lastSyncTime, 'errorWords')) {
        changedData.errorWords = allData.errorWords;
      }

      // 检查每日任务数据是否有变化
      if (this.hasDataChanged(allData.dailyTasksData, lastSyncTime, 'dailyTasksData')) {
        changedData.dailyTasksData = allData.dailyTasksData;
      }

      // 筛选练习历史中有变化的记录
      const changedPracticeHistory = this.filterChangedPracticeHistory(allData.practiceHistoryBase, lastSyncTime);
      if (changedPracticeHistory.length > 0) {
        changedData.practiceHistory = changedPracticeHistory;
      }

      return changedData;

    } catch (error) {
      console.error('[SyncManager] 筛选变化数据失败:', error);
      return allData; // 出错时返回所有数据
    }
  }

  /**
   * 检查数据是否有变化
   * @param {*} data - 数据对象
   * @param {number} lastSyncTime - 上次同步时间戳
   * @param {string} dataType - 数据类型
   * @returns {boolean} 是否有变化
   */
  static hasDataChanged(data, lastSyncTime, dataType) {
    if (!data) return false;

    try {
      // 获取数据的修改时间
      const dataModifiedTime = SimpleStorage.getDataModifiedTime(dataType);

      if (dataModifiedTime && dataModifiedTime > lastSyncTime) {
        return true;
      }

      // 如果没有修改时间记录，检查数据本身的时间戳字段
      if (data.lastModified) {
        const lastModified = new Date(data.lastModified).getTime();
        return lastModified > lastSyncTime;
      }

      // 如果是数组类型，检查是否有新增项目
      if (Array.isArray(data)) {
        return data.some(item => {
          const itemTime = new Date(item.lastModified || item.timestamp || 0).getTime();
          return itemTime > lastSyncTime;
        });
      }

      // 默认认为有变化（保守策略）
      return true;

    } catch (error) {
      console.error(`[SyncManager] 检查${dataType}变化失败:`, error);
      return true; // 出错时认为有变化
    }
  }

  /**
   * 筛选练习历史中有变化的记录
   * @param {Array} practiceHistoryBase - 练习历史基础数据
   * @param {number} lastSyncTime - 上次同步时间戳
   * @returns {Array} 有变化的练习记录
   */
  static filterChangedPracticeHistory(practiceHistoryBase, lastSyncTime) {
    if (!Array.isArray(practiceHistoryBase)) return [];

    return practiceHistoryBase.filter(record => {
      // 检查记录是否已同步
      if (record.synced && record.lastSynced && record.lastSynced >= lastSyncTime) {
        return false; // 已同步且同步时间在上次同步之后，不需要重新上传
      }

      // 检查记录的修改时间
      const recordTime = new Date(record.lastModified || record.timestamp || 0).getTime();
      return recordTime > lastSyncTime;
    });
  }

  /**
   * 应用云端数据到本地
   */
  static applyCloudDataToLocal(cloudData) {
    try {
      console.log('[SyncManager] 开始应用云端数据到本地 (分离存储)...', cloudData);

      // **[Refactored]** Apply data to their new separate storage locations
      if (cloudData.userData) {
        SimpleStorage.saveUserData(cloudData.userData);
        console.log('[SyncManager] ✅ 已应用userData，版本:', cloudData.userData.dataVersion);
      }

      if (cloudData.textbookData) {
        SimpleStorage.setTextbookData(cloudData.textbookData);
        console.log('[SyncManager] ✅ 已应用textbookData');
      }

      if (cloudData.textbookWordRewards) {
        this.applyTextbookWordRewards(cloudData.textbookWordRewards);
      }

      // 应用当前教材
      if (cloudData.currentTextbook) {
        SimpleStorage.setCurrentTextbook(cloudData.currentTextbook);
        console.log('[SyncManager] ✅ 已应用currentTextbook');
      }

      // 应用学习进度
      if (cloudData.learningProgress) {
        SimpleStorage.setLearningProgress(cloudData.learningProgress);
        console.log('[SyncManager] ✅ 已应用learningProgress，数量:', Object.keys(cloudData.learningProgress).length);
      }

      // 应用错题
      if (cloudData.errorWords && Array.isArray(cloudData.errorWords)) {
        SimpleStorage.setErrorWords(cloudData.errorWords);
        console.log('[SyncManager] ✅ 已应用errorWords，数量:', cloudData.errorWords.length);
        
        // 【FIX】通知 app 更新 TabBar 红点
        getApp().updateTabBarBadgeDelayed();
      }

      // 应用每日任务数据
      if (cloudData.dailyTasksData) {
        const limitedDailyTasksData = SimpleStorage.limitDailyTasksData(cloudData.dailyTasksData);
        SimpleStorage.setDailyTasksData(limitedDailyTasksData);
        console.log('[SyncManager] ✅ 已应用dailyTasksData，天数:', Object.keys(limitedDailyTasksData).length);
      }

      // 应用练习历史记录
      if (cloudData.practiceHistory && Array.isArray(cloudData.practiceHistory)) {
        SimpleStorage.setPracticeHistory(cloudData.practiceHistory);
        console.log('[SyncManager] ✅ 已应用practiceHistory，数量:', cloudData.practiceHistory.length);
      }

      // 更新同步时间
      this.updateLastSyncTime();

      // 通知首页/个人中心刷新数据
      try {
        const app = getApp();
        if (app && typeof app.globalData !== 'undefined') {
          app.globalData.shouldRefreshUserData = true;
          console.log('[SyncManager] 已设置 shouldRefreshUserData 标记');
        }
      } catch (e) {
        console.warn('[SyncManager] 设置全局刷新标记失败', e);
      }
 
      console.log('[SyncManager] ✅ 云端数据已全部应用到本地');
      return { success: true };
    } catch (error) {
      console.error('[SyncManager] 应用云端数据失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 更新最后同步时间
   */
  static updateLastSyncTime() {
    const now = new Date().toISOString();
    SimpleStorage.set('lastSyncTime', now);
  }

  /**
   * 获取最后同步时间
   */
  static getLastSyncTime() {
    return SimpleStorage.get('lastSyncTime', null);
  }

  /**
   * 上传本地数据到云端
   */
  static async uploadLocalData() {
    try {
      console.log('[SyncManager] 开始上传本地数据');

      const localData = this.collectLocalData();
      if (!localData) {
        throw new Error('收集本地数据失败');
      }

      const result = await cloudApi.sync.upload(localData);

      if (result.result && result.result.success) {
        console.log('[SyncManager] 数据上传成功，版本号:', localData.userData.dataVersion);

        // 保存更新后的用户数据（包含新的版本号）
        SimpleStorage.saveUserData(localData.userData);
        this.updateLastSyncTime();

        // 修复：上传成功后清空 practiceHistoryBase，避免重复存储
        SimpleStorage.clearPracticeHistoryBase();
        console.log('[SyncManager] 已清空 practiceHistoryBase，避免重复存储');

        return { success: true, action: 'uploaded' };
      } else {
        throw new Error(result.result?.message || '上传失败');
      }
    } catch (error) {
      console.error('[SyncManager] 上传数据失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 下载云端数据
   */
  static async downloadCloudData() {
    try {
      console.log('[SyncManager] 开始下载云端数据');

      const result = await cloudApi.sync.download();

      if (result.result && result.result.success && result.result.data) {
        const applied = this.applyCloudDataToLocal(result.result.data);
        console.log('[SyncManager] 数据下载并应用成功');
        return { success: applied, action: 'downloaded' };
      } else {
        throw new Error(result.result?.message || '下载失败');
      }
    } catch (error) {
      console.error('[SyncManager] 下载数据失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 检查是否需要同步
   */
  static checkSyncNecessity() {
    try {
      const lastSyncTime = this.getLastSyncTime();
      const currentTime = Date.now();
      const minSyncInterval = 5 * 60 * 1000; // 5分钟最小间隔

      // 如果从未同步过，需要同步
      if (!lastSyncTime) {
        return { needsSync: true, reason: '首次同步' };
      }

      // 检查时间间隔
      //  const timeSinceLastSync = currentTime - new Date(lastSyncTime).getTime();
      //  if (timeSinceLastSync < minSyncInterval) {
      //   return { 
      //      needsSync: false, 
      //    reason: `距离上次同步仅${Math.floor(timeSinceLastSync / 1000)}秒，跳过同步` 
      //    };
      //  }

      // 检查是否有有意义的数据变化
      const localStatus = this.checkLocalDataStatus(); // 不需要详细日志
      console.log('[SyncManager] 数据状态检查:', {
        hasSignificantData: localStatus.hasSignificantData,
        dataTypeCounts: localStatus.dataTypeCounts
      });
      
      if (!localStatus.hasSignificantData) {
        return {
          needsSync: false,
          reason: '无有意义数据，跳过同步'
        };
      }

      return { needsSync: true, reason: '有数据变化需要同步' };
    } catch (error) {
      console.error('[SyncManager] 检查同步必要性失败:', error);
      // 失败时保守处理，执行同步
      return { needsSync: true, reason: '检查失败，执行同步确保数据安全' };
    }
  }

  /**
   * 执行登录同步（主要方法）
   */
  static async performLoginSync() {
    // 防止并发调用
    if (this._syncInProgress) {
      console.log('[SyncManager] 同步正在进行中，跳过重复调用');
      return {
        success: true,
        action: 'skip_concurrent',
        message: '同步正在进行中，跳过重复调用'
      };
    }

    this._syncInProgress = true;

    try {
      console.log('[SyncManager] 开始执行登录同步');

      // 检查用户类型，游客模式下跳过云同步
      const userData = SimpleStorage.getUserData();
      if (userData && userData.userType === 'guest') {
        console.log('[SyncManager] 游客模式，跳过云同步');
        return {
          success: true,
          action: 'guest_mode_skip',
          message: '游客模式下跳过云同步'
        };
      }

      // 检查同步必要性
      const necessity = this.checkSyncNecessity();
      console.log('[SyncManager] 同步必要性检查:', necessity);

      if (!necessity.needsSync) {
        console.log('[SyncManager] 跳过同步:', necessity.reason);
        return { success: true, action: 'skipped', reason: necessity.reason };
      }

      console.log('[SyncManager] 开始云同步...');

      // 获取本地数据状态，启用详细日志用于同步决策
      const localSummary = this.checkLocalDataStatus(true);

      // 调用云函数获取同步策略
      const strategyResult = await cloudApi.sync.check(localSummary);

      if (!strategyResult || !strategyResult.result || !strategyResult.result.success) {
        throw new Error(strategyResult?.result?.message || '获取同步策略失败');
      }

      const { action, cloudData } = strategyResult.result;
      console.log('[SyncManager] 同步策略:', action);

      let syncResult = { success: true, action: action };

      // 根据策略执行同步
      switch (action) {
        case this.SYNC_ACTIONS.UPLOAD:
          syncResult = await this.uploadLocalData();
          break;

        case this.SYNC_ACTIONS.DOWNLOAD:
          if (cloudData) {
            console.log('[SyncManager] 使用策略返回的云端数据直接应用');
            const applied = this.applyCloudDataToLocal(cloudData);
            syncResult = {
              success: applied,
              action: 'downloaded',
              error: applied ? null : '应用云端数据失败'
            };
          } else {
            syncResult = await this.downloadCloudData();
          }
          break;

        case this.SYNC_ACTIONS.NO_ACTION:
          console.log('[SyncManager] 无需同步');
          syncResult = { success: true, action: 'no_action' };
          break;

        case this.SYNC_ACTIONS.CONFLICT:
          console.log('[SyncManager] 检测到数据冲突，需要用户选择');
          // 冲突处理时先隐藏loading，避免阻塞用户交互
          wx.hideLoading();
          syncResult = await this.handleDataConflict(cloudData);
          break;

        default:
          throw new Error('未知的同步策略: ' + action);
      }

      // 除了冲突情况，其他情况才需要隐藏loading
      if (action !== this.SYNC_ACTIONS.CONFLICT) {
        wx.hideLoading();
      }

      // 处理同步结果
      if (syncResult.success) {
        console.log(`[SyncManager] 同步完成: ${syncResult.action}`);

        // 显示适当的提示
        if (syncResult.action === 'uploaded') {
          wx.showToast({
            title: '数据已备份到云端',
            icon: 'success',
            duration: 2000
          });
        } else if (syncResult.action === 'downloaded') {
          wx.showToast({
            title: '已恢复云端数据',
            icon: 'success',
            duration: 2000
          });
        }
      } else {
        console.error('[SyncManager] 同步失败:', syncResult.error);
        wx.showToast({
          title: '同步失败，使用本地数据',
          icon: 'none',
          duration: 2000
        });
      }

      return syncResult;

    } catch (error) {
      wx.hideLoading();
      console.error('[SyncManager] 同步过程出错:', error);

      wx.showToast({
        title: '网络异常，使用本地数据',
        icon: 'none',
        duration: 2000
      });

      return {
        success: false,
        error: error.message,
        action: 'fallback_local'
      };
    } finally {
      this._syncInProgress = false;
    }
  }

  /**
   * 处理数据冲突
   */
  static async handleDataConflict(cloudData) {
    console.log('[SyncManager] 显示数据冲突对话框');
    
    return new Promise((resolve) => {
      wx.showModal({
        title: '数据冲突',
        content: '检测到数据冲突，请选择保留哪个版本：\n\n• 云端数据：版本较新\n• 本地数据：时间较新',
        confirmText: '用云端',
        cancelText: '用本地',
        success: async (res) => {
          console.log('[SyncManager] 用户选择结果:', res.confirm ? '云端数据' : '本地数据');
          
          if (res.confirm) {
            // 使用云端数据
            try {
              const applied = SyncManager.applyCloudDataToLocal(cloudData);
              resolve({
                success: applied,
                action: 'downloaded',
                error: applied ? null : '应用云端数据失败'
              });
            } catch (error) {
              console.error('[SyncManager] 应用云端数据失败:', error);
              resolve({
                success: false,
                action: 'failed',
                error: '应用云端数据失败: ' + error.message
              });
            }
          } else {
            // 使用本地数据，上传到云端
            try {
              const uploadResult = await SyncManager.uploadLocalData();
              resolve(uploadResult);
            } catch (error) {
              console.error('[SyncManager] 上传本地数据失败:', error);
              resolve({
                success: false,
                action: 'failed',
                error: '上传本地数据失败: ' + error.message
              });
            }
          }
        },
        fail: (error) => {
          console.error('[SyncManager] 显示冲突对话框失败:', error);
          resolve({
            success: false,
            action: 'failed',
            error: '显示对话框失败'
          });
        }
      });
    });
  }

  /**
   * 手动同步
   */
  static async manualSync() {
    try {
      console.log('[SyncManager] 用户触发手动同步');

      const userData = SimpleStorage.getUserData();
      if (userData && userData.userType === 'guest') {
        console.log('[SyncManager] 游客模式，不允许手动同步');
        wx.showModal({
          title: '游客模式',
          content: '游客模式下无法使用云同步功能，请先登录',
          showCancel: false
        });
        return { success: false, error: 'guest_mode' };
      }

      // 手动同步时忽略时间间隔检查
      wx.showLoading({
        title: '正在同步...',
        mask: true
      });

      const localSummary = this.checkLocalDataStatus();

      const strategyResult = await cloudApi.sync.check(localSummary);

      if (!strategyResult?.result?.success) {
        throw new Error(strategyResult?.result?.message || '获取同步策略失败');
      }

      const { action, cloudData, message } = strategyResult.result;
      let syncResult;

      console.log('[SyncManager] 执行同步策略:', { action, message });

      switch (action) {
        case this.SYNC_ACTIONS.UPLOAD:
          syncResult = await this.uploadLocalData();
          break;
        case this.SYNC_ACTIONS.DOWNLOAD:
          if (cloudData) {
            const applied = this.applyCloudDataToLocal(cloudData);
            syncResult = { success: applied, action: 'downloaded' };
          } else {
            syncResult = await this.downloadCloudData();
          }
          break;
        case this.SYNC_ACTIONS.NO_ACTION:
          syncResult = { success: true, action: 'no_action' };
          break;
        case this.SYNC_ACTIONS.CONFLICT:
          // 冲突处理时先隐藏loading，避免阻塞用户交互
          wx.hideLoading();
          syncResult = await this.handleDataConflict(cloudData);
          break;
      }

      // 除了冲突情况，其他情况才需要隐藏loading
      if (action !== this.SYNC_ACTIONS.CONFLICT) {
        wx.hideLoading();
      }

      if (syncResult.success) {
        wx.showToast({
          title: syncResult.action === 'no_action' ? '数据已是最新' : '同步成功',
          icon: 'success'
        });
      } else {
        throw new Error(syncResult.error || '同步失败');
      }

      return syncResult;

    } catch (error) {
      wx.hideLoading();
      console.error('[SyncManager] 手动同步失败:', error);

      wx.showModal({
        title: '同步失败',
        content: error.message || '网络异常，请稍后重试',
        showCancel: false
      });

      return { success: false, error: error.message };
    }
  }

  /**
   * 获取同步状态
   */
  static getSyncStatus() {
    try {
      const lastSyncTime = SimpleStorage.get('lastSyncTime');
      const lastLocalChangeTime = SimpleStorage.get('lastLocalChangeTime');
      
      const needsSync = lastLocalChangeTime > lastSyncTime;
      
      // **[FIX]** 强制启用云同步功能
      // 之前的检查 (this.isCloudEnvReady()) 可能在某些情况下不准确，
      // 直接返回 true 以确保用户始终可以使用同步功能。
      const syncAvailable = true;

      const status = {
        lastSyncTime,
        lastLocalChangeTime,
        needsSync,
        syncAvailable
      };

      console.log('🔄 [SyncManager] 获取同步状态:', status);
      return status;
    } catch (error) {
      console.error('[SyncManager] 获取同步状态失败:', error);
      return {
        lastSyncTime: null,
        needsSync: false,
        syncAvailable: true // **[FIX]** 即使失败也返回可用
      };
    }
  }

  /**
   * 重置同步状态（调试用）
   */
  static resetSyncStatus() {
    try {
      SimpleStorage.remove('lastSyncTime');
      console.log('[SyncManager] 同步状态已重置');
    } catch (error) {
      console.error('[SyncManager] 重置同步状态失败:', error);
    }
  }

  /**
   * 应用教材独立的字词奖励数据
   */
  static applyTextbookWordRewards(textbookWordRewards) {
    try {
      console.log('[SyncManager] 正在应用教材字词奖励数据...', textbookWordRewards);
      Object.keys(textbookWordRewards).forEach(storageKey => {
        const rewards = textbookWordRewards[storageKey];
        if (rewards && Object.keys(rewards).length > 0) {
          wx.setStorageSync(storageKey, rewards);
          console.log(`[SyncManager] ✅ 已应用 ${storageKey}`);
        }
      });
    } catch (error) {
      console.error('[SyncManager] 应用教材字词奖励数据失败:', error);
    }
  }

  // **新增：应用云端同步的教材数据**
  static applyTextbookData(textbookData) {
    try {
      console.log('[SyncManager] 正在应用教材数据...', textbookData);
      Object.keys(textbookData).forEach(storageKey => {
        const data = textbookData[storageKey];
        if (data && Object.keys(data).length > 0) {
          wx.setStorageSync(storageKey, data);
          console.log(`[SyncManager] ✅ 已应用 ${storageKey}`);
        }
      });
    } catch (error) {
      console.error('[SyncManager] 应用教材数据失败:', error);
    }
  }

  // **修复：收集所有教材数据（积分、经验等），不再依赖当前用户ID**
  static collectAllTextbookData() {
    try {
      const allTextbookData = {};
      const storageInfo = wx.getStorageInfoSync();
      
      console.log('[SyncManager] 🔍 开始收集所有教材数据...');
      
      // **修复：查找所有以 `textbookData_` 开头的键，不再与特定openid绑定**
      const textbookKeys = storageInfo.keys.filter(key => 
        key.startsWith('textbookData_') && !key.endsWith('_lastModified')
      );
      
      console.log('[SyncManager] 🎯 发现教材数据键:', textbookKeys);

      textbookKeys.forEach(key => {
        try {
          const data = wx.getStorageSync(key) || {};
          console.log(`[SyncManager] 🔍 检查教材数据 ${key}:`, {
            数据存在: !!data,
            字段数量: Object.keys(data).length,
            积分: data.points,
            经验: data.experience
          });
          
          if (Object.keys(data).length > 0) {
            allTextbookData[key] = data;
            console.log(`[SyncManager] ✅ 收集教材数据 ${key} 成功`);
          } else {
            console.log(`[SyncManager] ⚠️ 跳过空教材数据 ${key}`);
          }
        } catch (error) {
          console.error(`[SyncManager] ❌ 收集教材数据失败 ${key}:`, error);
        }
      });
      
      console.log('[SyncManager] 📊 教材数据收集结果:', {
        发现的教材数量: textbookKeys.length,
        有数据的教材数量: Object.keys(allTextbookData).length,
        教材列表: Object.keys(allTextbookData)
      });
      
      return allTextbookData;
    } catch (error) {
      console.error('[SyncManager] 收集所有教材数据失败:', error);
      return {};
    }
  }
}

module.exports = SyncManager; 