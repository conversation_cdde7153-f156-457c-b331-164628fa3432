const cloudApi = require('../../utils/cloudApi');
const SimpleStorage = require('../../utils/simpleStorage');
const TrendChartData = require('../../utils/trendChartData');
const TrendChart = require('../../utils/trendChart');
const app = getApp();

Page({
  data: {
    // 当前查看的用户
    currentViewUser: null,
    currentViewUserId: '',
    currentViewUserTextbookInfo: '', // 新增：用于显示当前查看用户的教材信息
    
    // 用户数据
    userInfo: null,
    
    // 绑定数据
    authWatchers: [], // 我授权的观看者
    watchTargets: [], // 我能观看的目标
    
    // 视图选择器选项
    viewUserOptions: [],
    
    // 学习进度数据
    progressData: {
      totalPracticeCount: 0,
      totalCorrectCount: 0,
      totalWrongCount: 0,
      accuracyRate: 0,
      totalPracticeTime: 0,
      errorWordsCount: 0,
      recentProgress: [],
      knowledgePointsStats: {},
      achievements: []
    },
    
    // UI状态
    isLoading: false,
    bindingSlotsLeft: 3,

    // 是否查看本人
    isViewingSelf: true,
    
    // 分享参数
    shareFromUserId: '',

    // 趋势图相关数据
    trendChartOptions: TrendChart.getDataTypeOptions(),
    trendChartDataType: 'practiceCount',
    trendChartLoading: false,
    trendChartEmpty: false,
    trendChartTooltip: {
      show: false,
      label: '',
      value: ''
    },
    trendChartSummary: {
      totalPractices: 0,
      averageAccuracy: 0,
      activeDays: 0
    },

    // 趋势图实例
    trendChart: null,
    trendChartCanvas: null,
    trendChartContext: null,
    trendChartData: null
  },

  onLoad(options) {
    console.log('[LearningProgress] 页面加载', options);
    
    // 检查是否是通过分享进入
    if (options.shareFromUserId) {
      this.setData({
        shareFromUserId: options.shareFromUserId
      });
      
      // 显示绑定确认弹窗
      this.showBindConfirmDialog(options.shareFromUserId);
    }
    
    this.initPage();
  },

  onReady() {
    // 页面渲染完成后延迟初始化趋势图
    setTimeout(() => {
      this.initTrendChart();
    }, 300);
  },

  onShow() {
    console.log('[LearningProgress] 页面显示');
    // 刷新数据
    this.loadData();
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    console.log('[LearningProgress] 下拉刷新');
    this.loadData().finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 初始化页面
   */
  async initPage() {
    try {
      this.setData({ isLoading: true });
      
      // 获取当前用户信息
      const userInfo = app.getUserInfo();
      if (!userInfo) {
        wx.showToast({
          title: '请先登录',
          icon: 'none'
        });
        wx.navigateBack();
        return;
      }
      
      // 确保用户有有效的标识
      const userOpenid = userInfo.openid || userInfo._id;
      if (!userOpenid) {
        console.error('[LearningProgress] 用户标识不存在:', userInfo);
        wx.showToast({
          title: '用户信息异常，请重新登录',
          icon: 'none'
        });
        wx.navigateBack();
        return;
      }
      
      this.setData({
        userInfo,
        currentViewUser: userInfo,
        currentViewUserId: userOpenid,
        isViewingSelf: true
      });
      
      // 不在此处加载数据，统一移动到 onShow
      // await this.loadData();
      
    } catch (error) {
      console.error('[LearningProgress] 初始化失败:', error);
      wx.showToast({
        title: '初始化失败',
        icon: 'none'
      });
    } finally {
      this.setData({ isLoading: false });
    }
  },

  /**
   * 加载页面数据
   */
  async loadData() {
    try {
      // 并行加载绑定列表和学习进度数据
      await Promise.all([
        this.loadBindList(),
        this.loadProgressData()
      ]);
      
    } catch (error) {
      console.error('[LearningProgress] 加载数据失败:', error);
    }
  },

  /**
   * 加载绑定列表
   */
  async loadBindList() {
    try {
      console.log('[LearningProgress] 开始加载绑定列表');
      const response = await cloudApi.user.getBindList();
      // 处理服务器模式的响应包装
      const result = response.result || response;
      
      console.log('[LearningProgress] 绑定列表API响应:', result);
      
      if (result.success || result.code === 0) {
        const { authWatchers = [], watchTargets = [] } = result.data || {};
        
        console.log('[LearningProgress] 解析绑定列表数据:', { authWatchers, watchTargets });
        
        // 构建视图选择器选项
        const viewUserOptions = [
          { name: '本人', openid: this.data.userInfo.openid || this.data.userInfo._id }
        ];
        
        // 添加可观看的目标用户
        watchTargets.forEach(target => {
          console.log('[LearningProgress] 添加可观看目标:', target);
          viewUserOptions.push({
            name: target.nickname || target.display_user_id || '用户',
            openid: target.openid
          });
        });
        
        console.log('[LearningProgress] 最终视图选项:', viewUserOptions);
        
        this.setData({
          authWatchers,
          watchTargets,
          viewUserOptions,
          bindingSlotsLeft: 3 - authWatchers.length
        });
        
        console.log('[LearningProgress] 绑定列表加载成功:', { authWatchers, watchTargets });
      } else {
        console.error('[LearningProgress] 加载绑定列表失败:', result);
        
        // 即使绑定列表加载失败，也要设置基本的选项
        this.setData({
          viewUserOptions: [
            { name: '本人', openid: this.data.userInfo.openid || this.data.userInfo._id }
          ]
        });
        
        // 非致命错误，不显示Toast，只在控制台记录
        console.warn('[LearningProgress] 绑定功能暂不可用，但不影响查看本人数据');
      }
      
    } catch (error) {
      console.error('[LearningProgress] 加载绑定列表异常:', error);
      
      // 异常情况下也要设置基本选项
      this.setData({
        viewUserOptions: [
          { name: '本人', openid: this.data.userInfo.openid || this.data.userInfo._id }
        ]
      });
      
      // 非致命错误，不显示Toast
      console.warn('[LearningProgress] 绑定功能异常，但不影响查看本人数据');
    }
  },

  /**
   * 加载学习进度数据
   */
  async loadProgressData() {
    try {
      const targetId = this.data.currentViewUserId;
      const myUserId = this.data.userInfo.openid || this.data.userInfo._id;
      
      console.log('[LearningProgress] 加载学习进度数据:', {
        targetId,
        myUserId,
        isSame: targetId === myUserId,
        hasTargetId: !!targetId,
        hasMyUserId: !!myUserId,
        currentViewUser: this.data.currentViewUser,
        isViewingSelf: this.data.isViewingSelf,
        viewUserOptions: this.data.viewUserOptions,
        watchTargets: this.data.watchTargets
      });
      
      // 如果查看的是本人，直接使用本地数据
      if (targetId === myUserId) {
        console.log('[LearningProgress] 查看本人数据，使用本地数据');
        const localProgressData = this.getLocalProgressData();
        
        // **新增**：从本地存储获取当前教材信息
        const currentTextbook = SimpleStorage.getCurrentTextbook();
        const textbookInfo = currentTextbook ? 
          `${currentTextbook.publisher.name}-${currentTextbook.grade.name}-${this.getTermName(currentTextbook.term)}` :
          '未选择教材';

        this.setData({
          progressData: localProgressData,
          currentViewUserTextbookInfo: textbookInfo
        });

        console.log('[LearningProgress] 本地学习进度数据加载成功:', localProgressData);

        // 加载趋势图数据
        this.loadTrendChartData();
        return;
      }
      
      // 查看他人数据，调用API
      console.log('[LearningProgress] 查看他人数据，调用API，targetId:', targetId);
      const response = await cloudApi.user.getProgress(targetId);
      // 处理服务器模式的响应包装
      const result = response.result || response;
      
      console.log('[LearningProgress] API响应:', result);
      
      if (result.success || result.code === 0) {
        const progressData = result.data || this.data.progressData;
        
        this.setData({
          progressData: progressData,
          currentViewUserTextbookInfo: progressData.textbookInfo || '未选择教材'
        });
        
        console.log('[LearningProgress] 学习进度数据加载成功:', progressData);

        // 加载趋势图数据
        this.loadTrendChartData();
      } else {
        console.error('[LearningProgress] 加载学习进度失败:', result.error);
        
        if (result.code === 403) {
          wx.showToast({
            title: '无权查看该用户数据',
            icon: 'none'
          });
        } else {
          wx.showToast({
            title: result.error || '加载数据失败',
            icon: 'none'
          });
        }
      }
      
    } catch (error) {
      console.error('[LearningProgress] 加载学习进度异常:', error);
      wx.showToast({
        title: '加载数据异常',
        icon: 'none'
      });
    }
  },

  /**
   * 获取本地学习进度数据
   */
  getLocalProgressData() {
    try {
      // 获取本地存储的数据
      const userData = SimpleStorage.getUserData() || {};
      const errorWords = SimpleStorage.getErrorWords() || [];
      const learningProgress = SimpleStorage.getLearningProgress() || {};

      // **修复：保持与个人中心一致的数据来源**
      const textbookData = SimpleStorage.getCurrentTextbookData() || {};

      let totalPracticeCount = userData.totalPracticeCount || 0;
      
      // 降级处理：如果全局练习次数为0，则从详细记录中重新计算
      if (totalPracticeCount === 0 && learningProgress) {
        totalPracticeCount = this._calculateTotalPracticeCount(learningProgress);
      }

      // 统计数据：仍然使用全局计数
      const totalCorrectCount = userData.totalCorrectCount || 0;
      const totalWrongCount = userData.totalWrongCount || 0;

      // 练习时长：改为当前教材的 totalTime（秒），与个人中心一致；若没有教材数据则回退到全局字段
      const totalPracticeTime = textbookData.totalTime || userData.totalPracticeTime || 0;
      
      // 【关键修复】直接从 userData 获取准确率，确保与个人中心完全一致
      const accuracyRate = userData.accuracyRate || 0;
      
      // 错题数量
      const errorWordsCount = errorWords.length;
      
      // 计算当前教材的详细统计
      const currentTextbook = SimpleStorage.getCurrentTextbook();
      let textbookStats = { totalWords: 0 };
      let errorStats = { pendingErrors: 0, correctedErrors: 0 };
      
      if (currentTextbook) {
        textbookStats = this.calculateTextbookStatsFromProgress(currentTextbook);
        errorStats = this.calculateErrorStats(currentTextbook);
      }

      return {
        totalPracticeCount,
        totalCorrectCount,
        totalWrongCount,
        accuracyRate,
        totalPracticeTime, // 保持秒，前端直接按秒显示
        totalWordsPracticed: textbookStats.totalWords || 0, // 练习字词数
        totalTimeFormatted: this.formatTotalTime(totalPracticeTime), // 格式化时长
        pendingErrors: errorStats.pendingErrors, // 待处理错题
        correctedErrors: errorStats.correctedErrors, // 已订正错题
        errorWordsCount: errorStats.pendingErrors + errorStats.correctedErrors, // 总错题数（兼容旧版）
        recentProgress: [],
        knowledgePointsStats: {},
        achievements: []
      };
      
    } catch (error) {
      console.error('[LearningProgress] 获取本地数据失败:', error);
      return this.data.progressData; // 返回默认数据
    }
  },

  /**
   * 计算总练习次数
   * @param {object} learningProgress - 学习进度对象
   * @returns {number}
   */
  _calculateTotalPracticeCount(learningProgress) {
    if (!learningProgress || typeof learningProgress !== 'object') {
      return 0;
    }
    return Object.values(learningProgress).reduce((sum, course) => {
      return sum + (course.practiceCount || 0);
    }, 0);
  },

  /**
   * 计算教材统计数据
   */
  calculateTextbookStats(textbook, learningProgress) {
    if (!textbook || !textbook.publisher) {
      return { practiceCount: 0, totalTime: 0 };
    }

    let practiceCount = 0;
    let totalTime = 0;

    const textbookKeyParts = {
      publisherId: textbook.publisher.id,
      gradeId: textbook.grade.id,
      term: textbook.term
    };
    
    // 从学习进度中计算数据
    for (const courseKey in learningProgress) {
      if (Object.prototype.hasOwnProperty.call(learningProgress, courseKey)) {
        const keyParts = courseKey.split('_');
        if (keyParts[0] === textbookKeyParts.publisherId && 
            keyParts[1] === textbookKeyParts.gradeId && 
            keyParts[2] === textbookKeyParts.term) {
          const courseData = learningProgress[courseKey];
          practiceCount += courseData.practiceCount || 0;
          totalTime += courseData.totalTime || 0;
        }
      }
    }

    return { practiceCount, totalTime };
  },

  /**
   * 切换查看对象
   */
  onViewUserChange(e) {
    const selectedIndex = e.detail.value;
    const viewUserOptions = this.data.viewUserOptions;
    
    console.log('[LearningProgress] 切换查看对象:', {
      selectedIndex,
      viewUserOptionsLength: viewUserOptions.length,
      viewUserOptions
    });
    
    if (selectedIndex >= 0 && selectedIndex < viewUserOptions.length) {
      const selectedOption = viewUserOptions[selectedIndex];
      
      let selectedUser;
      if (selectedIndex === 0) {
        // 查看自己
        selectedUser = this.data.userInfo;
      } else {
        // 查看其他用户
        const targetUser = this.data.watchTargets[selectedIndex - 1];
        selectedUser = targetUser;
      }
      
      const myUserId = this.data.userInfo.openid || this.data.userInfo._id;
      const isViewingSelf = selectedOption.openid === myUserId;
      
      console.log('[LearningProgress] 设置查看对象:', {
        selectedOption,
        selectedUser,
        isViewingSelf,
        currentViewUserId: selectedOption.openid
      });
      
      this.setData({
        currentViewUser: selectedUser,
        currentViewUserId: selectedOption.openid,
        isViewingSelf
      });
      
      // 重新加载学习进度数据
      this.loadProgressData();
    }
  },

  /**
   * 解绑用户
   */
  async onUnbindUser(e) {
    const { watcherid } = e.currentTarget.dataset;
    const watcher = this.data.authWatchers.find(w => w.openid === watcherid);
    
    if (!watcher) {
      wx.showToast({
        title: '用户信息不存在',
        icon: 'none'
      });
      return;
    }
    
    // 二次确认
    const result = await this.showConfirmDialog(
      '确认解绑',
      `确定要解除与 ${watcher.nickname || '该用户'} 的绑定关系吗？`
    );
    
    if (!result) return;
    
    try {
      wx.showLoading({ title: '解绑中...' });
      
      const unbindResponse = await cloudApi.user.unbind(watcherid);
      // 处理服务器模式的响应包装
      const unbindResult = unbindResponse.result || unbindResponse;
      
      if (unbindResult.success || unbindResult.code === 0) {
        wx.showToast({
          title: '解绑成功',
          icon: 'success'
        });
        
        // 刷新绑定列表
        this.loadBindList();
      } else {
        wx.showToast({
          title: unbindResult.error || '解绑失败',
          icon: 'none'
        });
      }
      
    } catch (error) {
      console.error('[LearningProgress] 解绑失败:', error);
      wx.showToast({
        title: '解绑失败',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 显示绑定确认弹窗
   */
  async showBindConfirmDialog(shareFromUserId) {
    try {
      // 先检查是否已经绑定过
      const bindListResponse = await cloudApi.user.getBindList();
      // 处理服务器模式的响应包装
      const bindListResult = bindListResponse.result || bindListResponse;
      if (bindListResult.success || bindListResult.code === 0) {
        const { watchTargets = [] } = bindListResult.data || {};
        const isAlreadyBound = watchTargets.some(target => target.openid === shareFromUserId);
        
        if (isAlreadyBound) {
          wx.showToast({
            title: '已经绑定过该用户',
            icon: 'none'
          });
          return;
        }
      }
      
      const result = await this.showConfirmDialog(
        '绑定确认',
        '是否绑定并查看该用户的学习进度？'
      );
      
      if (result) {
        await this.confirmBind(shareFromUserId);
      }
    } catch (error) {
      console.error('[LearningProgress] 绑定确认失败:', error);
      wx.showToast({
        title: '操作失败，请重试',
        icon: 'none'
      });
    }
  },

  /**
   * 确认绑定
   */
  async confirmBind(targetUserId) {
    try {
      wx.showLoading({ title: '绑定中...' });
      
      const response = await cloudApi.user.bind(targetUserId);
      // 处理服务器模式的响应包装
      const result = response.result || response;
      
      if (result.success || result.code === 0) {
        wx.showToast({
          title: '绑定成功',
          icon: 'success'
        });
        
        // 刷新绑定列表
        this.loadBindList();
      } else {
        let errorMsg = '绑定失败';
        if (result.code === 1) {
          errorMsg = '您的授权数量已达上限（3人）';
        } else if (result.code === 2) {
          errorMsg = '对方被绑定数量已达上限（3人）';
        } else if (result.code === 3) {
          errorMsg = '已经绑定过该用户';
        } else if (result.error) {
          errorMsg = result.error;
        }
        
        wx.showToast({
          title: errorMsg,
          icon: 'none'
        });
      }
      
    } catch (error) {
      console.error('[LearningProgress] 绑定失败:', error);
      wx.showToast({
        title: '绑定失败',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 显示确认对话框
   */
  showConfirmDialog(title, content) {
    return new Promise((resolve) => {
      wx.showModal({
        title,
        content,
        success: (res) => {
          resolve(res.confirm);
        },
        fail: () => {
          resolve(false);
        }
      });
    });
  },

  handleTestBind: async function() {
    const testOpenId = 'oPFXx61CJ8Qf6b40j-f13Id5QEWk';
    wx.showLoading({ title: '正在绑定...' });

    try {
      const response = await cloudApi.user.bind(testOpenId);
      const result = response.result || response;

      if (result.success || result.code === 0) {
        wx.showToast({
          title: '测试绑定成功！',
          icon: 'success'
        });
        // 刷新绑定列表和数据
        this.loadData();
      } else {
        let errorMsg = '绑定失败';
        if (result.code === 1) {
          errorMsg = '您的授权数量已达上限';
        } else if (result.code === 2) {
          errorMsg = '对方被绑定数量已达上限';
        } else if (result.code === 3) {
          errorMsg = '已经绑定过该用户';
        } else if (result.error) {
          errorMsg = result.error;
        }
        wx.showToast({
          title: errorMsg,
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('Test binding failed', error);
      wx.showToast({
        title: '绑定出错',
        icon: 'none'
      });
    } finally {
        wx.hideLoading();
    }
  },

  /**
   * 分享配置
   */
  onShareAppMessage() {
    const userInfo = this.data.userInfo;
    
    if (!userInfo) {
      return {
        title: '小学听写助手',
        path: '/pages/home/<USER>'
      };
    }
    
    const userOpenid = userInfo.openid || userInfo._id;
    if (!userOpenid) {
      return {
        title: '小学听写助手',
        path: '/pages/home/<USER>'
      };
    }
    
    return {
      title: `查看 ${userInfo.nickname || '我'} 的学习进度`,
      path: `/pages/learning-progress/learning-progress?shareFromUserId=${userOpenid}`,
      imageUrl: '/images/share-progress.png'
    };
  },

  /**
   * 分享到朋友圈
   */
  onShareTimeline() {
    const userInfo = this.data.userInfo;
    
    if (!userInfo) {
      return {
        title: '小学听写助手'
      };
    }
    
    const userOpenid = userInfo.openid || userInfo._id;
    if (!userOpenid) {
      return {
        title: '小学听写助手'
      };
    }
    
    return {
      title: `查看 ${userInfo.nickname || '我'} 的学习进度`,
      query: `shareFromUserId=${userOpenid}`,
      imageUrl: '/images/share-progress.png'
    };
  },

  /**
   * 处理错误情况
   */
  handleError(error, defaultMessage = '操作失败') {
    console.error('[LearningProgress] Error:', error);
    
    let message = defaultMessage;
    if (error && error.message) {
      message = error.message;
    } else if (typeof error === 'string') {
      message = error;
    }
    
    wx.showToast({
      title: message,
      icon: 'none',
      duration: 2000
    });
  },

  /**
   * **新增**：获取学期名称
   */
  getTermName(termId) {
    const termNames = {
      term1: '上册',
      term2: '下册'
    };
    return termNames[termId] || termId;
  },

  /**
   * 从learningProgress计算当前教材的统计数据（复制自profile.js）
   */
  calculateTextbookStatsFromProgress(textbook) {
    try {
      const learningProgress = SimpleStorage.getLearningProgress();
      const app = getApp();
      
      // 获取当前教材相关的课程进度
      const publisherId = textbook.publisher?.id || textbook.publisher;
      const gradeId = textbook.grade?.id || textbook.grade;
      const term = textbook.term || 'term1';
      
      // 标准化教材标识符
      const normalizedPublisherId = app.getPublisherId ? app.getPublisherId(publisherId) : publisherId;
      const normalizedGradeId = app.getGradeId ? app.getGradeId(gradeId) : gradeId;
      
      // 过滤出当前教材的课程进度
      const textbookCourses = Object.keys(learningProgress).filter(courseKey => {
        const parts = courseKey.split('_');
        if (parts.length !== 4) return false;
        
        const [keyPublisher, keyGrade, keyTerm] = parts;
        return keyPublisher === normalizedPublisherId && 
               keyGrade === normalizedGradeId && 
               keyTerm === term;
      });
      
      // 累加统计数据
      let totalPracticeCount = 0;
      textbookCourses.forEach(courseKey => {
        const progress = learningProgress[courseKey];
        if (progress && typeof progress === 'object') {
          totalPracticeCount += progress.practiceCount || 0;
        }
      });
      
      // 计算字词相关统计（简化估算）
      const estimatedWordsPerPractice = 5; // 平均每次练习5个字词
      const totalWords = Math.max(totalPracticeCount * estimatedWordsPerPractice, totalPracticeCount);
      
      return {
        practiceCount: totalPracticeCount,
        totalWords: totalWords,
        courseCount: textbookCourses.length
      };
      
    } catch (error) {
      console.error('[calculateTextbookStatsFromProgress] 计算失败:', error);
      return {
        practiceCount: 0,
        totalWords: 0,
        courseCount: 0
      };
    }
  },

  /**
   * 计算错题统计（基于当前教材，复制自profile.js）
   */
  calculateErrorStats(textbook) {
    const errorWords = SimpleStorage.getErrorWords();
    
    if (!textbook) {
      const pendingErrors = errorWords.filter(error => !error.corrected).length;
      const totalErrors = errorWords.length;
      return {
        pendingErrors,
        totalErrors,
        correctedErrors: totalErrors - pendingErrors
      };
    }
    
    // 使用与profile.js一致的过滤逻辑
    const currentTextbookKey = SimpleStorage.getTextbookKey(textbook);
    
    // 过滤出当前教材的错题
    const currentTextbookErrors = errorWords.filter(error => {
      if (!error.courseInfo) return false;
      
      // 优先使用 textbookKey 进行教材匹配
      if (typeof error.courseInfo === 'object' && error.courseInfo.textbookKey) {
        return error.courseInfo.textbookKey === currentTextbookKey;
      }
      
      // 回退方案：使用传统字段匹配
      const app = getApp();
      const errorPublisherId = error.courseInfo.publisherId || (app.getPublisherId ? app.getPublisherId(error.courseInfo.publisher) : error.courseInfo.publisher);
      const errorGradeId = error.courseInfo.gradeId || (app.getGradeId ? app.getGradeId(error.courseInfo.grade) : error.courseInfo.grade);
      const errorTermId = error.courseInfo.term || 'term1';
      
      const currentPublisherId = textbook.publisher.id || textbook.publisher;
      const currentGradeId = textbook.grade.id || textbook.grade;
      const currentTermId = textbook.term || 'term1';
      
      return errorPublisherId === currentPublisherId &&
             errorGradeId === currentGradeId &&
             errorTermId === currentTermId;
    });
    
    const pendingErrors = currentTextbookErrors.filter(error => !error.corrected).length;
    const totalErrors = currentTextbookErrors.length;
    
    return {
      pendingErrors,
      totalErrors,
      correctedErrors: totalErrors - pendingErrors
    };
  },

  /**
   * 格式化累计时间（秒转换为友好显示，复制自profile.js）
   */
  formatTotalTime(totalSeconds) {
    if (!totalSeconds || totalSeconds === 0) {
      return '0分钟';
    }
    
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;
    
    if (hours > 0) {
      if (minutes > 0) {
        return `${hours}小时${minutes}分钟`;
      } else {
        return `${hours}小时`;
      }
    } else if (minutes > 0) {
      return `${minutes}分钟`;
    } else {
      return `${seconds}秒`;
    }
  },

  /**
   * 从学习进度数据中提取教材信息
   * @param {object} progressData - 学习进度数据
   * @returns {object} 教材信息
   */
  extractTextbookInfo(progressData) {
    if (!progressData || !progressData.textbookInfo) {
      return '';
    }
    return JSON.stringify(progressData.textbookInfo);
  },

  /**
   * 初始化趋势图
   */
  async initTrendChart(retryCount = 0) {
    try {
      console.log('[LearningProgress] 开始初始化趋势图, 重试次数:', retryCount);

      // 添加延迟确保DOM渲染完成
      await new Promise(resolve => setTimeout(resolve, 100));

      const query = wx.createSelectorQuery().in(this);
      query.select('#trendChart')
        .fields({ node: true, size: true })
        .exec((res) => {
          console.log('[LearningProgress] Canvas查询结果:', res);

          if (res && res[0] && res[0].node) {
            const canvas = res[0].node;
            const ctx = canvas.getContext('2d');

            console.log('[LearningProgress] Canvas节点获取成功:', {
              width: res[0].width,
              height: res[0].height,
              node: !!canvas,
              context: !!ctx
            });

            // 设置画布尺寸
            const dpr = wx.getSystemInfoSync().pixelRatio;
            canvas.width = res[0].width * dpr;
            canvas.height = res[0].height * dpr;
            ctx.scale(dpr, dpr);

            // 保存画布实例
            this.setData({
              trendChartCanvas: canvas,
              trendChartContext: ctx
            });

            // 创建趋势图实例
            this.data.trendChart = new TrendChart(canvas, ctx, {
              padding: { top: 30, right: 30, bottom: 50, left: 50 }
            });

            console.log('[LearningProgress] 趋势图初始化完成');

            // 加载趋势图数据
            this.loadTrendChartData();
          } else {
            console.error('[LearningProgress] 趋势图Canvas初始化失败, 查询结果:', res);

            // 重试机制
            if (retryCount < 3) {
              console.log('[LearningProgress] 将在500ms后重试...');
              setTimeout(() => {
                this.initTrendChart(retryCount + 1);
              }, 500);
            } else {
              console.error('[LearningProgress] 趋势图初始化最终失败，已达到最大重试次数');
              this.setData({
                trendChartEmpty: true,
                trendChartLoading: false
              });
            }
          }
        });

    } catch (error) {
      console.error('[LearningProgress] 初始化趋势图失败:', error);

      // 重试机制
      if (retryCount < 3) {
        console.log('[LearningProgress] 异常后将在500ms后重试...');
        setTimeout(() => {
          this.initTrendChart(retryCount + 1);
        }, 500);
      } else {
        this.setData({
          trendChartEmpty: true,
          trendChartLoading: false
        });
      }
    }
  },

  /**
   * 加载趋势图数据
   */
  async loadTrendChartData() {
    try {
      console.log('[LearningProgress] 开始加载趋势图数据');

      this.setData({
        trendChartLoading: true,
        trendChartEmpty: false
      });

      // 获取趋势图数据
      const targetUserId = this.data.isViewingSelf ? null : this.data.currentViewUserId;
      const trendData = await TrendChartData.getTrendData(7, targetUserId);

      console.log('[LearningProgress] 趋势图数据:', trendData);

      // 检查是否有数据
      const isEmpty = trendData.summary.totalPractices === 0;

      this.setData({
        trendChartData: trendData,
        trendChartEmpty: isEmpty,
        trendChartLoading: false,
        trendChartSummary: trendData.summary
      });

      // 如果有数据且趋势图已初始化，则绘制图表
      if (!isEmpty && this.data.trendChart) {
        this.data.trendChart.drawChart(trendData, this.data.trendChartDataType);
      }

    } catch (error) {
      console.error('[LearningProgress] 加载趋势图数据失败:', error);
      this.setData({
        trendChartLoading: false,
        trendChartEmpty: true
      });
    }
  },

  /**
   * 趋势图数据类型切换
   */
  onTrendChartDataTypeChange(e) {
    const dataType = e.currentTarget.dataset.type;
    console.log('[LearningProgress] 切换趋势图数据类型:', dataType);

    this.setData({
      trendChartDataType: dataType
    });

    // 重新绘制图表
    if (this.data.trendChart && this.data.trendChartData && !this.data.trendChartEmpty) {
      this.data.trendChart.drawChart(this.data.trendChartData, dataType);
    }
  },

  /**
   * 趋势图触摸开始
   */
  onTrendChartTouchStart(e) {
    if (!this.data.trendChart || this.data.trendChartEmpty) return;

    const touch = e.touches[0];
    const touchInfo = this.data.trendChart.handleTouch(touch.x, touch.y);

    if (touchInfo) {
      this.setData({
        'trendChartTooltip.show': true,
        'trendChartTooltip.label': touchInfo.label,
        'trendChartTooltip.value': TrendChart.formatValue(touchInfo.value, touchInfo.dataType)
      });

      // 重新绘制图表并显示提示
      this.data.trendChart.drawChart(this.data.trendChartData, this.data.trendChartDataType);
      this.data.trendChart.drawTooltip(touchInfo);
    }
  },

  /**
   * 趋势图触摸移动
   */
  onTrendChartTouchMove(e) {
    if (!this.data.trendChart || this.data.trendChartEmpty) return;

    const touch = e.touches[0];
    const touchInfo = this.data.trendChart.handleTouch(touch.x, touch.y);

    if (touchInfo) {
      this.setData({
        'trendChartTooltip.label': touchInfo.label,
        'trendChartTooltip.value': TrendChart.formatValue(touchInfo.value, touchInfo.dataType)
      });

      // 重新绘制图表并显示提示
      this.data.trendChart.drawChart(this.data.trendChartData, this.data.trendChartDataType);
      this.data.trendChart.drawTooltip(touchInfo);
    }
  },

  /**
   * 趋势图触摸结束
   */
  onTrendChartTouchEnd(e) {
    this.setData({
      'trendChartTooltip.show': false
    });

    // 重新绘制图表（移除提示）
    if (this.data.trendChart && this.data.trendChartData && !this.data.trendChartEmpty) {
      this.data.trendChart.drawChart(this.data.trendChartData, this.data.trendChartDataType);
    }
  }

});