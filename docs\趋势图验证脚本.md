# 趋势图功能验证脚本

## 在微信开发者工具控制台中运行

### 1. 基础语法验证

```javascript
// 验证模块是否能正常加载
console.log('🔍 开始验证趋势图模块...');

try {
  const TrendChartData = require('./utils/trendChartData');
  console.log('✅ TrendChartData模块加载成功');
  
  const TrendChart = require('./utils/trendChart');
  console.log('✅ TrendChart模块加载成功');
  
  const TrendChartDebug = require('./utils/trendChartDebug');
  console.log('✅ TrendChartDebug模块加载成功');
  
  console.log('🎉 所有模块加载成功，语法无误！');
} catch (error) {
  console.error('❌ 模块加载失败:', error.message);
  console.error('请检查语法错误');
}
```

### 2. API连接测试

```javascript
// 测试API连接
async function testApiConnection() {
  console.log('🔍 测试API连接...');
  
  try {
    const cloudApi = require('./utils/cloudApi');
    console.log('API配置:', cloudApi.config);
    
    const response = await cloudApi.user.getPracticeHistory({
      limit: 5,
      showLoading: false
    });
    
    console.log('API响应:', response);
    
    if (response.success) {
      console.log('✅ API连接成功');
      console.log('数据数量:', response.data ? response.data.length : 0);
      if (response.data && response.data.length > 0) {
        console.log('示例数据:', response.data[0]);
      }
    } else {
      console.log('❌ API调用失败:', response.error);
    }
  } catch (error) {
    console.error('💥 API测试异常:', error);
  }
}

await testApiConnection();
```

### 3. 趋势图数据生成测试

```javascript
// 测试趋势图数据生成
async function testTrendDataGeneration() {
  console.log('🔍 测试趋势图数据生成...');
  
  try {
    const TrendChartData = require('./utils/trendChartData');
    const trendData = await TrendChartData.getTrendData(7);
    
    console.log('趋势图数据:', trendData);
    console.log('数据摘要:', trendData.summary);
    
    // 验证数据结构
    const requiredFields = ['labels', 'practiceCount', 'accuracy', 'totalTime', 'maxValues', 'summary'];
    const isValid = requiredFields.every(field => trendData.hasOwnProperty(field));
    
    if (isValid) {
      console.log('✅ 趋势图数据生成成功');
      console.log('- 标签数量:', trendData.labels.length);
      console.log('- 总练习次数:', trendData.summary.totalPractices);
      console.log('- 平均正确率:', trendData.summary.averageAccuracy + '%');
      console.log('- 活跃天数:', trendData.summary.activeDays);
    } else {
      console.log('❌ 数据结构不完整');
    }
    
    return trendData;
  } catch (error) {
    console.error('💥 趋势图数据生成异常:', error);
    return null;
  }
}

await testTrendDataGeneration();
```

### 4. 生成测试数据

```javascript
// 生成测试数据进行验证
async function generateTestData() {
  console.log('🔍 生成测试数据...');
  
  try {
    const TrendChartDebug = require('./utils/trendChartDebug');
    const success = await TrendChartDebug.generateTestDataAndVerify();
    
    if (success) {
      console.log('✅ 测试数据生成成功');
      
      // 重新测试趋势图生成
      const trendData = await testTrendDataGeneration();
      
      if (trendData && trendData.summary.totalPractices > 0) {
        console.log('🎉 基于测试数据的趋势图生成成功！');
      }
    } else {
      console.log('❌ 测试数据生成失败');
    }
  } catch (error) {
    console.error('💥 测试数据生成异常:', error);
  }
}

await generateTestData();
```

### 5. 完整验证流程

```javascript
// 完整验证流程
async function fullValidation() {
  console.log('🚀 开始完整验证流程...');
  
  const results = {
    moduleLoading: false,
    apiConnection: false,
    dataGeneration: false,
    testData: false
  };
  
  // 1. 模块加载验证
  console.log('\n1️⃣ 模块加载验证');
  try {
    require('./utils/trendChartData');
    require('./utils/trendChart');
    require('./utils/trendChartDebug');
    results.moduleLoading = true;
    console.log('✅ 模块加载成功');
  } catch (error) {
    console.error('❌ 模块加载失败:', error.message);
  }
  
  // 2. API连接验证
  console.log('\n2️⃣ API连接验证');
  try {
    const cloudApi = require('./utils/cloudApi');
    const response = await cloudApi.user.getPracticeHistory({ limit: 1, showLoading: false });
    results.apiConnection = response.success;
    console.log(response.success ? '✅ API连接成功' : '❌ API连接失败');
  } catch (error) {
    console.error('❌ API连接异常:', error.message);
  }
  
  // 3. 数据生成验证
  console.log('\n3️⃣ 数据生成验证');
  try {
    const TrendChartData = require('./utils/trendChartData');
    const trendData = await TrendChartData.getTrendData(7);
    results.dataGeneration = !!trendData;
    console.log(trendData ? '✅ 数据生成成功' : '❌ 数据生成失败');
  } catch (error) {
    console.error('❌ 数据生成异常:', error.message);
  }
  
  // 4. 测试数据验证
  console.log('\n4️⃣ 测试数据验证');
  try {
    const TrendChartDebug = require('./utils/trendChartDebug');
    results.testData = await TrendChartDebug.generateTestDataAndVerify();
    console.log(results.testData ? '✅ 测试数据验证成功' : '❌ 测试数据验证失败');
  } catch (error) {
    console.error('❌ 测试数据验证异常:', error.message);
  }
  
  // 输出验证结果
  console.log('\n📋 验证结果汇总:');
  console.log('- 模块加载:', results.moduleLoading ? '✅' : '❌');
  console.log('- API连接:', results.apiConnection ? '✅' : '❌');
  console.log('- 数据生成:', results.dataGeneration ? '✅' : '❌');
  console.log('- 测试数据:', results.testData ? '✅' : '❌');
  
  const passedCount = Object.values(results).filter(Boolean).length;
  console.log(`\n🎯 验证通过: ${passedCount}/4`);
  
  if (passedCount === 4) {
    console.log('🎉 所有验证通过，趋势图功能正常！');
  } else {
    console.log('⚠️ 部分验证失败，请检查相关问题');
  }
  
  return results;
}

await fullValidation();
```

### 6. 清理测试数据

```javascript
// 清理测试数据
function cleanupTestData() {
  console.log('🧹 清理测试数据...');
  
  try {
    wx.removeStorageSync('practiceHistory');
    console.log('✅ 测试数据已清理');
  } catch (error) {
    console.error('❌ 清理失败:', error);
  }
}

// 如果需要清理测试数据，运行：
// cleanupTestData();
```

### 7. 强制刷新趋势图

```javascript
// 强制刷新当前页面的趋势图
async function refreshTrendChart() {
  console.log('🔄 强制刷新趋势图...');
  
  try {
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    
    if (currentPage && currentPage.loadTrendChartData) {
      await currentPage.loadTrendChartData();
      console.log('✅ 趋势图刷新成功');
    } else {
      console.log('❌ 当前页面不支持趋势图刷新');
    }
  } catch (error) {
    console.error('💥 刷新失败:', error);
  }
}

// 如果在学习进度页面，可以运行：
// await refreshTrendChart();
```

## 使用说明

1. **在学习进度页面**：打开微信开发者工具，进入学习进度页面，在控制台中运行相应脚本
2. **逐步验证**：建议先运行基础语法验证，再逐步进行其他测试
3. **问题排查**：如果某个步骤失败，查看错误信息并参考快速修复指南
4. **测试数据**：如果没有真实练习数据，可以生成测试数据进行验证

## 预期结果

- ✅ 所有模块正常加载
- ✅ API连接成功（如果服务器正常）
- ✅ 趋势图数据生成成功
- ✅ 测试数据验证通过

如果所有验证都通过，说明趋势图功能已经正常工作！
